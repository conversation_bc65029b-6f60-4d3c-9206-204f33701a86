import Cookies from 'js-cookie'
import Config from '@/settings'

const Token<PERSON>ey = Config.TokenKey

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token, rememberMe) {
  if (rememberMe) {
    return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: Config.tokenCookieExpires })
  } else return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}


export const COMPANY_NAMES = '海南成功网联科技股份有限公司营口分,海南成功网联科技股份有限公司上海分,海南成功网联科技股份有限公司厦门分,海南成功网联科技股份有限公司大连分,海南成功网联科技股份有限公司'
