<template>
   <div class="app-container">
    <div>
      <el-form
        :inline="true"
        :model="params"
        class="query-form demo-form-inline"
      >
        <el-form-item label="挂账月">
          <el-date-picker
            v-model="params.billMonth"
            value-format="yyyy-MM"
            type="month"
            placeholder="选择月"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否已开票">
          <el-select v-model="params.status" placeholder="">
             <el-option label="全部" style="padding-left: 20px;" value="">全部</el-option>
             <el-option label="未开票" style="padding-left: 20px;" value="0">未开票</el-option>
             <el-option label="已开票" style="padding-left: 20px;" value="1">已开票</el-option>
          </el-select>
        </el-form-item>
          <el-form-item label="税率">
          <el-select v-model="params.tax" placeholder="">
             <el-option label="全部" style="padding-left: 20px;" value="">全部</el-option>
             <el-option label="6%" style="padding-left: 20px;" value="6%">6%</el-option>
             <el-option label="9%" style="padding-left: 20px;" value="9%">9%</el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="onQuery" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :data="list"
      border
      stripe
      show-summary
      :summary-method="getSummaries"
      ref="table"
      style="width: 100%"
    >
     <el-table-column type="index" label="#" width="50" align="center" />
      <el-table-column
        prop="guaTime"
        label="年"
        width="65px"
        :formatter="fmtYear"
        align="center"
      >
      </el-table-column>
       <el-table-column
        prop="guaTime"
        label="月份"
        width="65px"
        :formatter="fmtMonth"
        align="center"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="invoiceCode"
        align="center"
        :formatter="formatDef"
        label="发票代码"
      >
      </el-table-column> -->
      <el-table-column
        prop="shipTime"
        align="center"
        :formatter="fmtMonthDay"
        label="离港日期"
      >
      </el-table-column>
      <el-table-column
        prop="shipName"
        align="center"
        :formatter="formatDef"
        label="船名"
      >
      </el-table-column>
      <el-table-column
        prop="voyageNumber"
        align="center"
        :formatter="formatDef"
        label="航次号"
      >
      </el-table-column>

      <el-table-column
        prop="cusSupName"
        align="center"
        :formatter="formatDef"
        label="客户名称"
      >
      </el-table-column>

      <el-table-column
        prop="costTypeName"
        align="center"
        :formatter="formatDef"
        label="费用"
      >
      </el-table-column>
      <el-table-column
        prop="settleTonnage"
        align="center"
        :formatter="formatDef"
        label="货量"
      >
      </el-table-column>
      <el-table-column
        prop="priceWithTax"
        align="center"
        :formatter="formatDef"
        label="运价"
      >
      </el-table-column>
      <el-table-column
        prop="totalPrice"
        align="center"
        :formatter="formatDefNo"
        label="金额"
      >
      </el-table-column>
        <el-table-column prop="billTax" align="center" width="40px" label="税率">
      </el-table-column>

      <el-table-column
        align="center"
        :formatter="formatDefNo"
        label="不含税"
      >
        <template slot-scope="scope">
          {{fmtMoney(shuiLv(scope.row.billTax,scope.row.totalPrice))}}
        </template>
      </el-table-column>
   <el-table-column
        align="center"
        :formatter="formatDefNo"
        label="税额"
      >
       <template slot-scope="scope">
          {{fmtMoney(scope.row.totalPrice - shuiLv(scope.row.billTax,scope.row.totalPrice))}}
        </template>
      </el-table-column>
       <el-table-column prop="status" align="center"  label="发票状态">
        <template slot-scope="scope">
          <div v-if="scope.row.salesCount > 0">已开票</div>
          <div v-else style="color:#f25584;">未开票</div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next,sizes"
      :total="total"
      :page-size="params.pageSize"
      :current-page.sync="params.pageNum"
      :page-sizes="[10, 100, 200, 300, 500]"
      @size-change="handleSizeChange"
      @current-change="eventPage"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
    >
    </el-pagination>
  </div>
</template>
<script>
import { invoiceSalesDataSource} from "@/api/system/invoice";
import dayjs from 'dayjs'
import currency from 'currency.js'
import {getPrevMonthDay} from '@/utils/utils'
export default{
  name: 'estimatedRevenue',
  data(){
    return {
      params:{
        billMonth:getPrevMonthDay(),
        status:'',
        pageNum:1,
        pageSize:10,
        tax:''
      },
      list:[],
      total:0
    }
  },
  created(){
    this.loadList()
  },
  methods:{
    handleSizeChange(v){
      this.params.pageSize = v;
      this.loadList();
    },
    eventPage(){
      this.loadList()
    },
    shuiLv(v,p){
      if(!p){
        return 0
      }
      if(!v){
        return p
      }
      if(v.indexOf('%')>-1){
        return p / (1+Number(v.replace('%',''))/100)
      }
      return p
    },
    formatDef(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue + "";
      }
      return "--";
    },
    fmtYear(row, column, cellValue, index){
      if (!cellValue) {
        return '--'
      }
      return dayjs(cellValue).format('YYYY')
    },
    fmtMonth(row, column, cellValue, index){
      return dayjs(cellValue).format('M')
    },
    fmtMonthDay(row, column, cellValue, index){
      return dayjs(cellValue).format('M月DD日')
    },
     fmtMoney(v) {
      if (!v && v != 0) {
        return "--";
      } else {
        return currency(v, { symbol: "" }).format();
      }
    },
    formatDefNo(row, column, cellValue, index) {
      if (cellValue) {
        return this.fmtMoney(cellValue);
      }
      return "--";
    },
    onQuery(){
      this.loadList()
    },
    loadList(){
      const data = {
        pageNum:this.params.pageNum,
        pageSize:this.params.pageSize,
        billMonth:this.params.billMonth,
        status:this.params.status,
        invoiceTax:this.params.tax
      }
      invoiceSalesDataSource(data).then(res=>{
        console.log('19',res)
        this.list = res.data
        this.total = res.total
      })
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        // if (index != 14 && index != 12 && index != 13) {
        //   sums[index] = ""
        //   return
        // }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = this.fmtMoney(sums[index]);
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },
  }
}

</script>
