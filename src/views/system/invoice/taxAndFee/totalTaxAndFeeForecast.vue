<template>
  <section class="app-container">
    <!-- 税费预测合计 -->
 <div>
      <el-form
        :inline="true"
        :model="params"
        class="query-form demo-form-inline"
      >
        <el-form-item label="挂账月">
          <el-date-picker
            v-model="params.billMonth"
            value-format="yyyy-MM"
            type="month"
            placeholder="选择月"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button @click="onQuery" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 月份 配载类型 已开票 销项税额  实收票 进项税额  应纳税额 -->
    <el-table :data="list" border style="width: 100%">
       <el-table-column type="index" label="#">
        </el-table-column>
        <el-table-column prop="billMonth" align="center" label="日期">
        </el-table-column>
         <el-table-column prop="typeName" align="center" label="类型">
        </el-table-column>
         <el-table-column prop="salesPrice" align="center" label="已开票">
        </el-table-column>
         <el-table-column prop="salesTax" align="center" label="销项税额">
        </el-table-column>
         <el-table-column prop="incomePrice" align="center" label="实收票">
        </el-table-column>
         <el-table-column prop="incomeTax" align="center" label="进项税额">
        </el-table-column>
           <el-table-column prop="difference" align="center" label="应纳税额">
        </el-table-column>
    </el-table>

  </section>
</template>
<script>
import {taxAndFeeForecast} from '@/api/system/invoice'
// import dayjs from 'dayjs'
import currency from 'currency.js'
import {getPrevMonthDay} from '@/utils/utils'
export default{
  name: 'totalTaxAndFeeForecast',
  data(){
    return {
      params:{
        billMonth:getPrevMonthDay(),
      },
      list:[],
    }
  },
  created(){
    this.loadList()
  },
  methods:{
     fmtMoney(v) {
      if (!v && v != 0) {
        return "-";
      } else {
        return currency(v, { symbol: "" }).format();
      }
    },
    onQuery(){
      this.loadList()
    },
    loadList(){
      const data = {
        billMonth:this.params.billMonth,
      }
      taxAndFeeForecast(data).then(res=>{
        console.log('19',res)
        this.list = this.rowData(res)
      })
    },
    rowData(res){
      const row1 = { // wuliu
          billMonth:this.params.billMonth,
          type:'wuliu',
          typeName:'物流配载',
          salesPrice:this.fmtMoney(res.wuliu.sales.invoicePriceTaxSum || 0),
          salesTax: this.fmtMoney(res.wuliu.sales.invoicePriceTax || 0),
          incomePrice:this.fmtMoney(res.wuliu.income.invoicePriceTaxSum || 0),
          incomeTax:this.fmtMoney(res.wuliu.income.invoicePriceTax || 0),
          difference: this.fmtMoney((res.wuliu.sales.invoicePriceTax|| 0) - (res.wuliu.income.invoicePriceTax||0))
        }
      const row2 ={ // ship
        billMonth:this.params.billMonth,
        type:'ship',
        typeName:'船舶配载',
        salesPrice:'-',
        salesTax:'-',
        incomePrice:this.fmtMoney((res.ship.ship.invoicePriceTaxSum||0) + (res.ship.oilCost.invoicePriceTaxSum||0)),
        incomeTax:this.fmtMoney((res.ship.ship.invoicePriceTax||0) + (res.ship.oilCost.invoicePriceTax||0)),
        difference:'-'
        // (res.ship.ship.invoicePriceTax||0) + (res.ship.oilCost.invoicePriceTax||0)
      }
      const row3 = { // oil
        billMonth:this.params.billMonth,
        type:'oil',
        typeName:'油品',
        salesPrice:'-',
        salesTax:'-',
        incomePrice:this.fmtMoney(res.ship.oil.invoicePriceTaxSum || 0),
        incomeTax:this.fmtMoney(res.ship.oil.invoicePriceTax || 0),
        difference:'-'
        // res.ship.oil.invoicePriceTax
      }
      // const row4 ={ // oilCost
      //   billMonth:this.params.billMonth,
      //   type:'oilCost',
      //   salesPrice:'-',
      //   salesTax:'-',
      //   incomePrice:res.ship.oilCost.invoicePriceTaxSum,
      //   incomeTax:res.ship.oilCost.invoicePriceTax,
      //   difference:res.ship.oilCost.invoicePriceTax
      // }
      return [row1,row2,row3]
    }
  }

}
</script>
<style scoped>
</style>
