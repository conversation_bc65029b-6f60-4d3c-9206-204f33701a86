<template>
  <section class="app-container">
    <div class="sel-ship">
      <el-radio-group  v-model="params.shipId" @change="changeShip" >
        <el-radio-button v-for="item in shipList" :label="item.id">{{item.name}}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="query-form">
     <span class="title">选择日期</span>
     <el-date-picker
        v-model="params.month"
        @change="changeShip"
        placeholder="选择日期"
        type="month"
        value-format="yyyy-MM"
        clearable
        size="small"
      />
    </div>
   <div>
    <div class="item-cls">
       <span class="header-title">收入统计</span>
         <el-table
          :data="allIncomeList"
          v-loading="loading"
          element-loading-text="数据加载中"
          style="width: 100%"
          border
          stripe
          size="medium"
        >
          <el-table-column
            label="挂帐时间"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.dataYear">{{ scope.row.dataYear }}-{{ scope.row.dataMonth.toString().padStart(2,'0') }}</span>
              <span v-else >{{ scope.row.creditTime ? dayjs(scope.row.creditTime).format('YYYY-MM') : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="船名"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.shipName || '--' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="voyageNum"
            label="航次"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.voyageSummary">{{ scope.row.voyageSummary||'--' }}</span>
              <span v-else>{{ voyageStr(scope.row.voyageNum,scope.row.loadingPortName,scope.row.unloadingPortName) }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
            label="起始港-卸载港"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.loadingPortName || '--' }}-{{ scope.row.unloadingPortName || '--' }}
            </template>
          </el-table-column> -->

           <el-table-column
            prop="ptypeName"
            label="收入类型"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span :title="scope.row.fullPtypeName">{{ scope.row.ptypeName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsName"
            label="货种"
            width="80"
            align="center"
          />

          <el-table-column
            prop="tonnage"
            label="实装(吨)"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.tonnage || '-' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="price"
            label="运价(元/吨)"

            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(scope.row.price) }}
            </template>
          </el-table-column>

          <el-table-column
            prop="settleSumPrice"
            label="包船价(元)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(scope.row.settleSumPrice) }}
            </template>
          </el-table-column>
           <el-table-column
            label="动态费用(元)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="dynamic-costs-link" >
                {{ formatMoney(getVoyageDynamicCostsTotal(scope.row)) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="金额(价税)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(scope.row.shipIncomePrice) }}
            </template>
          </el-table-column>
          <el-table-column
            label="税率"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.priceTaxRate ? (scope.row.priceTaxRate * 100).toFixed(0) + '%' : '-' }}
            </template>
          </el-table-column>

          <el-table-column
            label="不含税金额"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
             {{ formatMoney(scope.row.shipIncomePrice / (1 + (scope.row.priceTaxRate || 0.09))) }}
            </template>
          </el-table-column>

          <el-table-column
            label="税额"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(scope.row.shipIncomePrice * ((scope.row.priceTaxRate || 0.09) / (1 + (scope.row.priceTaxRate || 0.09)))) }}
            </template>
          </el-table-column>

          <el-table-column
            prop="customerName"
            label="客户公司名称"
            align="center"
            :show-overflow-tooltip="true"
          />



          <el-table-column
            label="合计(元)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(getVoyageTotal(scope.row)) }}
            </template>
          </el-table-column>
        </el-table>
        <!-- <ship-month-table :outcomeList="incomeList"></ship-month-table> -->
    </div>
     <div class="item-cls ">
         <span class="header-title">成本统计</span>
         <div v-if="actualCostList && actualCostList.length>0" class="costs-list">
         <div v-for="(item, index) in actualCostList" :key="index" class="costs-list__item">
            <div class="cost-detail-table-wrapper">
              <table class="cost-detail-table">
                <tbody>
                  <tr v-for="cat in item._shipCostChild" :key="cat.id">
                    <td>
                      <div class="name-cell">{{ cat.typeName }}</div>
                      <div class="amount-cell">{{ formatMoney(cat._sum || 0) }}</div>
                    </td>
                    <td v-for="n in getMaxSubCats(item._shipCostChild)" :key="n">
                      <div v-if="cat.children && cat.children[n - 1]">
                        <div class="name-cell">{{ cat.children[n - 1].typeName }}</div>
                        <div class="amount-cell">{{ formatMoney(cat.children[n - 1]._sum || 0) }}</div>
                      </div>
                      <div v-else>
                        <div class="name-cell" />
                        <div class="amount-cell" />
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div class="name-cell">油品费用</div>
                      <div class="amount-cell">{{ formatMoney(item._oilCostSum || 0) }}</div>
                    </td>
                    <!-- <td v-for="cat in item._shipOilCostChild" :key="cat.id">
                      <div>
                        <div class="name-cell">{{ cat.typeName }}</div>
                      <div class="amount-cell">{{ formatMoney(cat._sum || 0) }}</div>
                      </div>
                    </td> -->
                    <td v-for="n in Math.max(getMaxSubCats(item._shipCostChild),item._shipOilCostChild.length)" :key="n">
                     <div v-if="item._shipOilCostChild && item._shipOilCostChild[n - 1]">
                        <div class="name-cell">{{ item._shipOilCostChild[n - 1].typeName }}</div>
                        <div class="amount-cell">{{ formatMoney(item._shipOilCostChild[n - 1]._sum || 0) }}</div>
                      </div>
                      <div v-else>
                        <div class="name-cell" />
                        <div class="amount-cell" />
                      </div>
                    </td>
                  </tr>
                    <!-- <tr v-for="cat in item._shipOilCostChild" :key="cat.id" >
                    <td>
                      <div class="name-cell">{{ cat.typeName }}</div>
                      <div class="amount-cell">{{ formatMoney(cat._sum || 0) }}</div>
                    </td>
                    <td v-for="n in getMaxSubCats(item._shipOilCostChild)" :key="n">
                      <div v-if="cat.children && cat.children[n - 1]">
                        <div class="name-cell">{{ cat.children[n - 1].typeName }}</div>
                        <div class="amount-cell">{{ formatMoney(cat.children[n - 1]._sum || 0) }}</div>
                      </div>
                      <div v-else>
                        <div class="name-cell" />
                        <div class="amount-cell" />
                      </div>
                    </td>
                  </tr> -->
                    <!-- <tr >
                    <td>
                      <div class="name-cell">油品费用</div>
                      <div class="amount-cell">{{ formatMoney(item._oilCostSum || 0) }}</div>
                    </td>
                       <template v-for="(oilItem, oilIndex) in item.oilCostRecordChildDtoList">
                        <td>
                          <div class="name-cell" >
                            <span class="cost-name">{{ oilItem.type }}({{ oilItem.tonnage }}{{ oilItem.unit
                              }})</span>
                              <span v-if="oilItem.supplierName" class="supplier-name">(承运商：{{ oilItem.supplierName
                              }})</span>
                              <div class="oil-info">
                                <span class="oil-value">{{ oilItem.voyageNum || '-' }}</span>
                                <span class="oil-value">{{ oilItem.loadingPortName || '-' }}</span>-<span
                                  class="oil-value"
                                >{{ oilItem.unloadingPortName || '-' }}</span>
                              </div>
                          </div>
                        <div class="amount-cell" >
                           <span class="amount-cell"><span v-if="oilItem.priceTaxRate > 0" class="tax-rate">({{oilItem.priceTaxRate?((oilItem.priceTaxRate * 100).toFixed(0) + '%'):'-'}})&nbsp;</span>¥ {{
                                formatMoney(oilItem.waterBalance || '0') || '-'
                              }}</span>
                        </div>
                        </td>
                      </template>
                  </tr> -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div v-else>
          <div v-if="loading" style="display: flex; justify-content: center; align-items: center; height: 100%;">
            <!-- <span style="font-size: 16px; color: #909399;">加载中...</span> -->
          </div>
           <el-empty v-else>
          </el-empty>
        </div>

        <!-- <ship-month-table :outcomeList="outcomeList" :oilList="oilList"></ship-month-table> -->
     </div>

   </div>


  </section>
</template>
<script>
import {shipList,costOfRevenue,costTypeList} from '@/api/system/onAccount.js'
import ShipMonthTable from '@/views/components/shipMonthTable/index.vue'
import dayjs from 'dayjs'
import { loading } from 'vxe-table'
import {getPrevMonthDay} from '@/utils/utils'
export default{
  components:{ShipMonthTable},
  data(){
    return {
      loading: false,  // 添加这一行
      shipList:[],
      params:{
        shipId:'',
        month:getPrevMonthDay()
      },
      inCostList:[],
      outCostList:[],
      outOilCostList:[],
      incomeList:[],
      outcomeList:[],
      oilList:[],
      voyages:[],
      extraIncomes:[],
      actualCostList: [],
      costTypeList:[],
    }
  },
  computed:{
      shipIncomeList(){
        return this.extraIncomes.map(item=>{
          return {
            ...item,
            shipIncomePrice:item.price,
            price:'',
            // voyageNum:item.voyageSummary,

          }
        })
      },
      allIncomeList(){
        if(!this.voyages){
          return this.shipIncomeList
        }
        return this.voyages.concat(this.shipIncomeList)
      },
      voyageExtraIncomes() {
        return this.extraIncomes.filter(item => item.voyageId)
      },
      // 非航次收入
      nonVoyageExtraIncomes() {
        return this.extraIncomes.filter(item => !item.voyageId)
      },
      dynamicCosts() {
        const costs = []
        ;(this.voyages||[]).forEach(voyage => {
          if (voyage.voyageCostGroupList && voyage.voyageCostGroupList.length > 0) {
            voyage.voyageCostGroupList.forEach(group => {
              if (group.costDtoList && group.costDtoList.length > 0) {
                group.costDtoList.forEach(cost => {
                  costs.push({
                    ...cost,
                    voyageNum: voyage.voyageNum,
                    voyageId: voyage.id,
                    planLoadingDate: voyage.planLoadingDate,
                    ship: voyage.ship,
                    groupCompanyName: group.companyName,
                    groupSumPrice: group.sumPrice
                    // Ensure priceTaxRate is included
                  })
                })
              }
            })
          }
        })
        return costs
      }
  },
  created(){
    console.log('16-created')
    this.loadData()
    // this.loadCostTypeList()
  },
  methods:{
    voyageStr(num,start,end){
      if(!num){
        // return start + '→' + end
        return '--'
      }
      if(!start && !end){
        return '--'
      }
      if(!start){
        return num + ' ' + '→' + end
      }
      if(!end){
        return num + ' ' + start + '→'
      }
      return num + ' ' + start + '→' + end
    },
    loadCostTypeList(){
      costTypeList().then(res=>{
        this.costTypeList = res
      })
    },
      getMaxSubCats(shipCostChild) {
      if (!shipCostChild || !shipCostChild.length) return 0
      let max = 0
      shipCostChild.forEach(cat => {
        if (cat.children && cat.children.length > max) {
          max = cat.children.length
        }
      })
      return max
    },
     formatMoney(value) {
      if (!value && value !== 0) return '-'
      return Number(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    loadData(){
      shipList().then(res=>{
        console.log('19',res)
        this.shipList = res.data
        this.params.shipId = this.shipList[0] && this.shipList[0].id
        this.loadDetail()
      })
    },
    changeShip(){
      this.loadDetail()
    },
    loadDetail(){
      if(!this.params.shipId){
        return
      }
      if(!this.params.month){
        return
      }
      this.loading = true  // 添加这一行
      // init
      this.inCostList = []
      this.outCostList = []
      this.outOilCostList = []
      this.incomeList = []
      this.outcomeList = []
      this.oilList = []
      this.voyages = []
      this.extraIncomes = []
      this.actualCostList = []
      const data={
        id:this.params.shipId,
        month:this.params.month,
      }
      costOfRevenue(data).then(res=>{
        // this.inCostList = this.sortCostTypes(res.inCostList)
        this.outCostList = this.sortCostTypes(res.outCostList)
        this.outOilCostList = this.sortCostTypes(res.outOilCostList)
        // this.incomeList = this.sortCostPriceTypes(res.incomeList,this.inCostList)
        // this.outcomeList = this.sortCostPriceTypes(res.outcomeList,this.outCostList)
        // this.oilList = res.outOilList
        this.voyages = res.voyageList
         // 将嵌套的shipCostRecordChildDtoList展平，保留父级ID
        const flattenedIncomes = []
        const inTemList = res.incomeList || []
        inTemList.forEach(record => {
          const s = record.shipCostRecordChildDtoList || []
          s.forEach(child => {
            flattenedIncomes.push({
              ...child,
              dataYear: record.dataYear,
              dataMonth: record.dataMonth,
              ship: record.ship,
              parentId: record.id, // 保存父级ID
              confirm: record.confirm
            })
          })
        })
        this.extraIncomes = flattenedIncomes

        const tmpList = res.outList || []
        // 计算每行的小计
        tmpList.forEach(item => {
          // 计算船舶费用小计
          const s = item.shipCostRecordChildDtoList || []
          const shipCostSum = s.reduce((sum, child) => {
            const price = Number.parseFloat(child.price)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0
          const shipCostChild = this.sortCostPriceTypes(s,this.outCostList)
          const o = item.oilCostRecordChildDtoList || []
          // 计算油品费用小计
          const oilCostSum = o.reduce((sum, child) => {
            const price = Number.parseFloat(child.waterBalance)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0
          const shipOilCostChild = this.sortCostPriceTypes(o,this.outOilCostList,'waterBalance','type','typeName')
          // 总小计
          item._sum = shipCostSum + oilCostSum
          // 保存分项小计，用于显示
          item._shipCostSum = shipCostSum
          item._oilCostSum = oilCostSum
          item._shipCostChild = shipCostChild
          item._shipOilCostChild = shipOilCostChild
        })
        this.actualCostList = tmpList
        console.log('cost list',this.actualCostList)
        this.loading = false  // 添加这一行
      }).catch(() => {
        this.loading = false  // 添加这一行
      })
    },
     sortCostTypes(types) {
      if (types == null || types.length === 0) {
        return []
      }
      const typeMap = {}
      const root = []
      // biome-ignore lint/complexity/noForEach: <explanation>
      types.forEach(type => {
        // typeMap[type.id] = { ...type, children: [] }
        typeMap[type.id] = type
        typeMap[type.id].children = []
        if (!type.parentId) {
          root.push(typeMap[type.id])
        }
      })
      // biome-ignore lint/complexity/noForEach: <explanation>
      types.forEach(type => {
        if (type.parentId && typeMap[type.parentId]) {
          typeMap[type.parentId].children.push(typeMap[type.id])
        }
      })
      return root
    },
    sortCostPriceTypes(list,costTypeList,pkey='price',ptypeKey='ptypeId',costKey='id') {
      if (list == null || list.length === 0) {
        return []
      }
      const listMap = {}
      // biome-ignore lint/complexity/noForEach: <explanation>
      list.forEach(item => {
        if (!listMap[item[ptypeKey]]) {
          listMap[item[ptypeKey]] = []
        }
        listMap[item[ptypeKey]].push(item)
      })
      const sortedTypes = []
      const lts = JSON.parse(JSON.stringify(costTypeList||[]))
      // biome-ignore lint/complexity/noForEach: <explanation>
      lts.forEach(type => {
        const t = type
        let p = 0
        if (listMap[t[costKey]]) {
          p = Number.parseFloat(listMap[t[costKey]].reduce((sum, child) => {
            const price = Number.parseFloat(child[pkey])
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t._detail = listMap[t[costKey]]
        }
        const sum = this.addChildrenPriceToSortedList(t, listMap,pkey,costKey)
        t._sum = sum + p
        sortedTypes.push(t)
      })
      return sortedTypes
    },
    addChildrenPriceToSortedList(item, listMap,pkey,costKey) {
      if (!item.children || !item.children.length) return 0
      let sum = 0
      // biome-ignore lint/complexity/noForEach: <explanation>
      item.children.forEach(child => {
        const t = child
        let p = 0
        if (listMap[t[costKey]]) {
          p = Number.parseFloat(listMap[t[costKey]].reduce((s, c) => {
            const price = Number.parseFloat(c[pkey])
            return s + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t._detail = listMap[t[costKey]]
        }
        const csum = this.addChildrenPriceToSortedList(t, listMap,pkey,costKey)
        t._sum = csum + p
        sum += t._sum
      })
      return sum
    },
     getVoyageDynamicCosts(voyage) {
      return this.dynamicCosts.filter(cost => cost.voyageId === voyage.id)
    },
    getVoyageDynamicCostsTotal(voyage) {
      return this.getVoyageDynamicCosts(voyage).reduce((sum, cost) => {
        const amount = Number.parseFloat(cost.prices) || 0
        return sum + (cost.costType === 1 ? amount : -amount)
      }, 0)
    },
     getVoyageTotal(voyage) {
      if(voyage.shipIncomePrice){
        return voyage.shipIncomePrice
      }
      const freight = ((voyage.price || 0) * (voyage.tonnage || 0)) + (voyage.settleSumPrice || 0)
      const dynamicCosts = this.getVoyageDynamicCostsTotal(voyage)
      return freight + dynamicCosts
    }
  }
}
</script>
<style lang="scss" scoped>
.sel-ship{
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}
.query-form{
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}
.query-form .title{
  font-size: 16px;
}
.item-cls {
  margin-top: 8px;
  margin-bottom: 8px;
}
.header-title{
  font-size: 16px;

}
.costs-list {
    &__item {
      margin-bottom: 16px;
      background-color: #fff;

      .allow-edit {
        color: var(--color-primary);
        cursor: pointer;
      }

      .el-row {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .text-bold {
        background-color: #f5f7fa;
        padding: 8px 12px;
        margin: -16px -16px 16px -16px;
        border-bottom: 1px solid #dcdfe6;
        border-radius: 8px 8px 0 0;
      }

      .cost-breakdown-row {
        border: 1px solid #ebeef5;
        background-color: #fafafa;
      }
    }
  }
  .cost-detail-table-wrapper {
  width: 100%;
  overflow-x: auto;
  margin-top: 12px;
}

.cost-detail-table {
  border-collapse: collapse;
  min-width: 700px;
  width: max-content;
  background: #fff;
  font-size: 14px;
}

.name-cell {
  border-bottom: 1px solid #dcdfe6;
  padding: 8px 16px;
}

.cost-detail-table th,
.cost-detail-table td {
  border: 1px solid #dcdfe6;

  min-width: 120px;
  max-width: 260px;
  text-align: center;
  word-break: break-all;
}

.cost-detail-table th {
  background: #f5f7fa;
  font-weight: 500;
}

.cost-detail-table td:first-child div:first-child {
  font-weight: bold;
  color: #1f2d3d;
  font-size: 13px;
}

.cost-detail-table td:not(:first-child) div:first-child {
  color: #909399;
  font-size: 13px;
  font-weight: normal;
}

.cost-detail-table .amount-cell {
  margin-top: 4px;
  font-size: 13px;
  color: #606266 !important;
  font-family: Monaco, monospace;
  font-weight: 500;
  padding: 8px 16px;
}
</style>
