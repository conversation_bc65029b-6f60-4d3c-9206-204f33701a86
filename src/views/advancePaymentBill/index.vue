<template>
  <section style="margin-bottom: 40px;padding-top: 15px;padding-left: 15px;display: inline-block;height: calc(max(100vh,100%) - 84px); width: 100% ">
    <div class="sp-title" style="font-size: 25px">
      我的申请
    </div>
    <el-divider />
    <!-- <el-col :span="12" style="width: 15%;height: 100%">
      <el-menu
        style="height: 100%"
        default-active="999"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
      >
        <el-menu-item index="999" @click="changemeun('all')">
          <span slot="title">全部</span>
        </el-menu-item>
        <el-submenu v-for="(item,index) in list" :index="String(index)" :key="index">
          <template slot="title">
            <span>{{ item.name }}</span>
          </template>
          <el-menu-item
            v-for="(items,indexs) in item.sysProcessDetailList"
            :key="String(index)+'-'+String(indexs)"
            :index="String(index)+'-'+String(indexs)"
            @click="changemeun(items.spare1)"
          >
            {{ items.name }}
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </el-col> -->
    <div style="width: 99%;margin-bottom: 10px;">
      <el-form :inline="true" :model="quary" class="query-form demo-form-inline">
        <el-form-item label="审批时间" label-width="70px">
          <el-date-picker
            v-model="quary.shipTime"
            style="margin-top:3px;"
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="状态" label-width="70px">
          <el-select v-model="quary.ongoing" style="width:100px;" placeholder="请选择">
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="进行中"
              value="1"
            />
            <el-option
              label="已结束"
              value="2"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="摘要" label-width="70px">
          <el-input v-model="quary.summary" placeholder="请输入摘要" clearable ></el-input>
        </el-form-item>
        <!-- <el-form-item label="付款信息" label-width="70px">
          <el-input v-model="quary.paymentSumm" placeholder="请输入付款信息" clearable></el-input>
        </el-form-item> -->
        <el-form-item label="对外付款账户" label-width="100px">
          <el-input v-model="quary.accQuery" placeholder="请输入付款账户" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onQuery">查询</el-button>
        </el-form-item>
      </el-form>
      <el-button style="float:right;margin-bottom: 3px;" @click="toExcel" size="mini"
        >导出Excel</el-button
      >
      <el-table
        v-loading="tableLoading"
        class="el-table-info mytable"
        :data="tableData"
        ref="table"
        stripe
        style="width: 100%;margin-top: 20px"
        :show-overflow-tooltip="true"
        border
      >
        <el-table-column
          prop="processname"
          :formatter="publicFmt"
          label="审批类型"
          align="center"
          width="100"
        />
        <el-table-column
          prop="briefContent"
          :formatter="publicFmt"
          label="流程摘要"
          align="center"
          width="350"
        >
        <template  slot-scope="scope">
          <div class="pointer" @click="showshenpi(scope.row.id,scope.row.zhanshiname)">
            {{scope.row.briefContent||'--'}}
          </div>
        </template>
        </el-table-column>
        <el-table-column
          prop="globalParam9"
          :formatter="ordercompanynameFmt"
          label="往来公司"
          align="center"
          width="100"
        />
        <el-table-column
          prop="globalParam3"
          :formatter="moneyFmt"
          label="金额"
          align="center"
          width="100"
        />
        <el-table-column
          v-if="quary.name && showAccNameList.indexOf(quary.name)>-1"
          prop="accName"
          :formatter="publicFmt"
          label="收款账户"
          align="center"
          width="100"
        />
        <el-table-column
          prop="time"
          :formatter="publicFmt"
          label="当前节点"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.status === '4'">已撤回</div>
            <div v-if="scope.row.status === '2'">已完成</div>
            <div v-if="scope.row.status === '3'">已驳回</div>
            <div v-if="scope.row.status === '1'">{{ scope.row.pname !== undefined ? scope.row.pname:scope.row.rname }} <!--
             -->{{Math.floor(scope.row.statusId/10) == 8?'会计做账中':'审批中'}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sponsorTime"
          :formatter="publicFmt"
          label="创建日期"
          align="center"
          width="95"
        />
        <el-table-column
          label="操作"
          class-name="excel-hidden"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showshenpi(scope.row.id,scope.row.zhanshiname)">查看</el-button>
            <el-button type="text" size="small" @click="upload(scope.row)">上传发票({{ scope.row.invoiceSum||'无' }})</el-button>
            <el-button v-if="scope.row.status==='1' && !scope.row.isShenpi " type="text" size="small" @click="cancelProcess(scope.row.id)">撤回申请</el-button>
            <el-button v-if="isShowByCode(scope.row.flowCode) && ( scope.row.status==='4' || scope.row.status==='3' || scope.row.status==='2')" type="text" size="small" @click="replayProcess(scope.row)">重新申请</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next,sizes"
        :total="total"
        :page-size="quary.pageSize"
        :current-page.sync="quary.pageNum"
        :page-sizes="[10, 100, 200, 300, 500]"
        @size-change="handleSizeChange"
        style="text-align: right;padding: 10px 0px;background-color: #fff"
        @current-change="eventPage"
      />
    </div>
    <ProgressComponent ref="processDrawerqingjia" @refresh="refresh" @onCallback="handlerGlobalParams" />

    <!-- 发票上传对话框 -->
    <el-dialog
      title="上传发票"
      :visible.sync="invoiceDialogVisible"
      width="60%"
      :before-close="handleInvoiceDialogClose"
      :close-on-click-modal="false"
    >
      <div v-if="currentProcess">
        <p><strong>流程摘要：</strong>{{ currentProcess.briefContent || '--' }}</p>
        <p><strong>金额：</strong>{{ formatMoney(currentProcess.globalParam3) }}</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="invoiceLoading" style="text-align: center; padding: 20px;">
        <i class="el-icon-loading"></i>
        <span style="margin-left: 10px;">正在加载发票信息...</span>
      </div>

      <el-divider content-position="left">发票上传</el-divider>

      <!-- 上传进度条 -->
      <div style="margin-bottom: 20px;" v-if="currentProcess">
        <el-progress
          :percentage="Math.min(100, (uploadedAmount / (currentProcess.globalParam3 || 1)) * 100)"
          :status="uploadedAmount > currentProcess.globalParam3 ? 'exception' : 'success'"
          :format="formatProgress"
        />
        <div style="text-align: center; margin-top: 5px; color: #606266;">
          已上传 {{ formatMoney(uploadedAmount) }} / 核销金额 {{ formatMoney(currentProcess.globalParam3) }}
        </div>
      </div>

      <el-upload
        ref="invoiceUpload"
        :action="uploadUrl"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :on-preview="handlePreview"
        multiple
        accept=".jpg,.jpeg,.png,.pdf"
        list-type="listType"
        :limit="10"
      >
        <i class="el-icon-plus" style="font-size: 18px;color: #409EFF;">点击上传</i>
        <div slot="tip" class="el-upload__tip">
          支持jpg、jpeg、png、pdf格式，单个文件不超过5MB，最多上传10个文件
        </div>
      </el-upload>

      <!-- 已有发票列表 -->
      <div>
        <el-divider content-position="left">
          已有发票
          <span v-if="invoiceLoading" style="margin-left: 10px;">
            <i class="el-icon-loading"></i> 加载中...
          </span>
          <span v-else-if="existingInvoices.length > 0" style="margin-left: 10px; color: #67C23A;">
            (共 {{ existingInvoices.length }} 张)
          </span>
          <span v-else style="margin-left: 10px; color: #909399;">
            (暂无发票)
          </span>
        </el-divider>

        <!-- 加载状态 -->
        <div v-if="invoiceLoading" style="text-align: center; padding: 20px;">
          <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
          <p style="margin-top: 10px; color: #909399;">正在加载已有发票...</p>
        </div>

        <!-- 发票列表 -->
        <el-table v-else-if="existingInvoices.length > 0" :data="existingInvoices" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column prop="fileName" label="发票内容" width="200"></el-table-column>
          <!-- <el-table-column prop="invoiceCode" label="发票代码" width="120"></el-table-column> -->
          <el-table-column prop="invoiceNumber" label="发票号码" width="120"></el-table-column>
          <el-table-column prop="totalAmount" label="金额" width="100" :formatter="moneyTableFmt"></el-table-column>
          <el-table-column prop="invoiceDate" label="开票日期" width="120"></el-table-column>
          <el-table-column prop="sellerName" label="销售方" width="150"></el-table-column>
          <el-table-column prop="invoiceDrawer" label="开票人" width="100"></el-table-column>
          <el-table-column prop="invoiceReview" label="复核人" width="100"></el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewInvoiceImage(scope.row)">查看</el-button>
              <!-- <el-button type="text" size="small" @click="editExistingInvoice(scope.row)">编辑</el-button> -->
              <!-- <el-button type="text" size="small" style="color: #f56c6c;" @click="deleteInvoice(scope.row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>

        <!-- 无发票提示 -->
        <div v-else style="text-align: center; padding: 20px; color: #909399;">
          <i class="el-icon-document" style="font-size: 48px; margin-bottom: 10px;"></i>
          <p>暂无已有发票，请上传发票文件进行识别</p>
        </div>
      </div>

      <!-- OCR识别结果 -->
      <div v-if="ocrResults.length > 0">
        <el-divider content-position="left">新识别发票</el-divider>
        <el-table :data="ocrResults" border style="width: 100%">
          <el-table-column prop="fileName" label="文件名" width="150"></el-table-column>
          <el-table-column prop="invoiceTitle" label="发票内容" width="200"></el-table-column>
          <!-- <el-table-column prop="invoiceType" label="发票类型" width="120"></el-table-column> -->
          <!-- <el-table-column prop="invoiceCode" label="发票代码" width="120"></el-table-column> -->
          <el-table-column prop="invoiceNumber" label="发票号码" width="120"></el-table-column>
          <el-table-column prop="totalAmount" label="金额" width="100" :formatter="moneyTableFmt"></el-table-column>
          <el-table-column prop="invoiceDate" label="开票日期" width="120"></el-table-column>
          <el-table-column prop="sellerName" label="销售方" width="150"></el-table-column>
          <el-table-column prop="invoiceDrawer" label="开票人" width="100"></el-table-column>
          <el-table-column prop="invoiceReview" label="复核人" width="100"></el-table-column>
          <!-- <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editOcrResult(scope.row)">编辑</el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleInvoiceDialogClose">取消</el-button>
        <el-button type="primary" @click="saveInvoices" :loading="uploadLoading">保存</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" append-to-body>
      <!-- <img width="100%" :src="previewImageUrl" alt=""> -->
      <file-list :value="[{url:previewImageUrl}]"></file-list>
    </el-dialog>

    <!-- 发票编辑对话框 -->
    <el-dialog
      :title="isEditingOcrResult ? '编辑新识别发票' : '编辑发票信息'"
      :visible.sync="editInvoiceDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      :before-close="handleEditInvoiceDialogClose"
    >
      <el-form :model="editInvoiceForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发票内容">
              <el-input v-model="editInvoiceForm.title" placeholder="请输入发票内容"></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="发票号码">
              <el-input v-model="editInvoiceForm.invoiceNo" placeholder="请输入发票号码"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="发票类型">
              <el-input v-model="editInvoiceForm.invoiceType" placeholder="请输入发票类型"></el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        <!-- <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发票代码">
              <el-input v-model="editInvoiceForm.invoiceCode" placeholder="请输入发票代码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票号码">
              <el-input v-model="editInvoiceForm.invoiceNo" placeholder="请输入发票号码"></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开票日期">
              <el-date-picker
                v-model="editInvoiceForm.invoiceDate"
                type="date"
                placeholder="选择开票日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价税合计">
              <el-input v-model="editInvoiceForm.invoicePriceTaxSum" placeholder="请输入价税合计"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12" v-if="isSpecialInvoice(editInvoiceForm.invoiceType)">
            <el-form-item label="税额" >
              <el-input v-model="editInvoiceForm.invoicePriceTax" placeholder="请输入税额"></el-input>
            </el-form-item>
            <!-- <el-form-item label="发票类型" v-else>
              <el-input v-model="editInvoiceForm.invoiceType" placeholder="发票类型" readonly></el-input>
            </el-form-item> -->
          </el-col>
          <el-col :span="12">
            <el-form-item label="不含税金额">
              <el-input v-model="editInvoiceForm.invoicePrice" placeholder="请输入不含税金额"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="销售方名称">
              <el-input v-model="editInvoiceForm.sellerName" placeholder="请输入销售方名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="购买方名称">
              <el-input v-model="editInvoiceForm.buyerName" placeholder="请输入购买方名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开票人">
              <el-input v-model="editInvoiceForm.invoiceDrawer" placeholder="请输入开票人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="复核人">
              <el-input v-model="editInvoiceForm.invoiceReview" placeholder="请输入复核人"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleEditInvoiceDialogClose">取消</el-button>
        <el-button type="primary" @click="saveEditedInvoice" :loading="editInvoiceLoading">保存</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import { getMyprocess, getProcessList } from '@/api/business/processapi'
import {addPrepaymentReimbursementInvoice, delInvoiceDialog, getInvoiceCountByProcessIds, listPrepaymentReimbursementInvoice, updatePrepaymentReimbursementInvoice} from '@/api/system/invoice'
import { recognizeOcrByImgurl } from '@/api/system/ocr'
import { cancelApply } from '@/api/system/process'
import { detailByCode as processDetails } from '@/api/system/sysProcessDetail'
import uploadFile from '@/components/UploadFile'
import ProgressComponent from '@/components/workflow/process'
import prepared from '@/mixins/prepared'
import { UPLOAD_URL } from '@/utils/config'
import Avue from '@smallwei/avue'
import currency from 'currency.js'
import dayjs from 'dayjs'
import { saveAs } from "file-saver";
import Vue from 'vue'
import XLSX from "xlsx";
import XLSXStyle from "xlsx-style";
import FileList from "@/components/filelist/index.vue";
Vue.use(Avue)

export default {
  name: 'Myprocess',
  components: {
    ProgressComponent,
    uploadFile,
    FileList
  },
  mixins: [prepared(2)],
  data() {
    return {
      contractCode: 'contractFlow', // 合同code
      specialCode: ['reserveChangeBill', 'reserveApply', 'reserveBill', 'contractFlow'], // 特殊流程code
      showAccNameList:['Reimbursement','TravelReimbursement','CurrentAccountApplication'],
      uploadedAmount: 0, // 已上传发票金额
      ExternalAccount: {},
      total: 0,
      tableData: [
      ],
      tableLoading: false,
      quary: {
        time: '',
        name: 'PrepaymentReimbursement',
        summary: '',
        paymentSumm: '',
        accQuery: '',
        pageSize: 10,
        pageNum: 1,
        ongoing: ''
      },
      radio1: 'Form',
      list: [],
      sizeValue: 'small',
      // 发票上传相关
      invoiceDialogVisible: false,
      currentProcess: null,
      uploadLoading: false,
      fileList: [],
      ocrLoading: false,
      ocrResults: [],
      existingInvoices: [], // 已有发票列表
      uploadUrl: UPLOAD_URL,
      previewVisible: false,
      previewImageUrl: '',
      invoiceLoading: false, // 发票查询加载状态
      // 发票编辑相关
      editInvoiceDialogVisible: false,
      editInvoiceForm: {
        id: '',
        title: '',
        invoiceCode: '',
        invoiceNo: '',
        invoiceDate: '',
        invoiceType: '',
        invoicePriceTaxSum: '',
        invoicePriceTax: '',
        invoicePrice: '',
        sellerName: '',
        buyerName: '',
        invoiceDrawer: '',
        invoiceReview: ''
      },
      editInvoiceLoading: false,
      isEditingOcrResult: false, // 标识是否在编辑新识别的发票
      editingOcrIndex: -1 // 正在编辑的OCR结果索引
    }
  },
  created() {
    // getProcessList().then(res => {
    //   if (res !== undefined && res.resultCode === '0') {
    //     this.list = res.data
    //     // console.log(this.list)
    //   }
    //   this.prepareSubmit()
    // })
    // const getFdbizCustomer = this.$store.dispatch('data/getFdbizCustomerListSaveInVuex')
    // getFdbizCustomer.then(data => {
    //   console.log(data.fdbizCustomerList);
    //   // this.ExternalAccount = res.data
    //   for (const comp of data.fdbizCustomerList) {
    //     this.ExternalAccount[comp.id + ''] = comp.companyName
    //   }
    //   this.prepareSubmit()
    // })
    // this.preparedThen(() => {
      this.getPage()
    // })
  },
  methods: {
    formatProgress(percentage) {
      return `${percentage}%`
    },
    // 判断是否为专票（增值税专用发票）
    isSpecialInvoice(invoiceType) {
      const specialInvoiceTypes = ['VatSpecialInvoice', 'VatElectronicSpecialInvoice', 'VatElectronicSpecialInvoiceFull']
      return specialInvoiceTypes.includes(invoiceType)
    },
    toExcel() {
      function parseElement(str) {
        var o = document.createElement("thead");
        o.innerHTML = str;
        return o.childNodes[0];
      }
      function lineHeight(sheet, skipFist = false, height = 30) {
        if (sheet["!rows"]) {
          sheet["!rows"] = [sheet["!rows"][0]];
        } else {
          sheet["!rows"] = [];
        }
        const range = XLSX.utils.decode_range(sheet["!ref"]);
        for (var i = skipFist ? 1 : 0; i <= range.e.r; i++) {
          sheet["!rows"].push({ hpt: height });
        }
      }
      function formatCell(worksheel, titleReg) {
        const borderAll = {
          //单元格外侧框线
          top: {
            style: "thin"
          },
          bottom: {
            style: "thin"
          },
          left: {
            style: "thin"
          },
          right: {
            style: "thin"
          }
        };

        const range = XLSX.utils.decode_range(worksheel["!ref"]);

        for (let C = range.s.c; C <= range.e.c; ++C) {
          for (let R = range.s.r; R <= range.e.r; ++R) {
            const cell = { c: C, r: R };
            const i = XLSX.utils.encode_cell(cell);

            if (i == "!ref" || i == "!cols" || i == "!rows") {
            } else {
              if (!worksheel[i]) {
                worksheel[i] = { t: "s", v: "" };
              }
              if (titleReg && titleReg.test(i)) {
                worksheel[i].s = {
                  // border: borderAll,
                  font: {
                    name: "宋体",
                    sz: 12,
                    color: { rgb: "000000" },
                    bold: false,
                    italic: false,
                    underline: false
                  },
                  alignment: {
                    horizontal: "left",
                    vertical: "center",
                    wrapText: true
                  }
                };
              } else {
                worksheel[i].s = {
                  border: borderAll,
                  font: {
                    name: "宋体",
                    sz: 12,
                    color: { rgb: "000000" },
                    bold: false,
                    italic: false,
                    underline: false
                  },
                  alignment: {
                    horizontal: "center",
                    vertical: "center",
                    wrapText: true
                  }
                };
              }

              if (/^(-?\d+)(\.\d+)?$/g.test(worksheel[i].v + "")) {
                worksheel[i].t = "n";
              }
            }
          }
          // //给所以单元格加上边框
          // for (var i in worksheel) {
          //     if (i == '!ref' || i == '!cols' || i == '!rows') {
          //     } else if (i == 'A1') {
          //       worksheel[i + ''].s = {
          //         // border: borderAll,
          //         font: {
          //           name: '宋体',
          //           sz: 12,
          //           color: {rgb: "000000"},
          //           bold: false,
          //           italic: false,
          //           underline: false
          //         },
          //         alignment: {
          //           horizontal: "left",
          //           vertical: "center",
          //           wrapText: true
          //         }

          //       }
          //     } else {

          //        worksheel[i + ''].s = {
          //         // border: borderAll,
          //         font: {
          //           name: '宋体',
          //           sz: 12,
          //           color: {rgb: "000000"},
          //           bold: false,
          //           italic: false,
          //           underline: false
          //         },
          //         alignment: {
          //           horizontal: "center",
          //           vertical: "center"
          //         }

          //       }
          //     }
        }
      }
      function s2ab(s) {
        if (typeof ArrayBuffer !== "undefined") {
          const buf = new ArrayBuffer(s.length);
          const view = new Uint8Array(buf);
          for (let i = 0; i !== s.length; ++i) {
            view[i] = s.charCodeAt(i) & 0xff;
          }
          return buf;
        } else {
          const buf = new Array(s.length);
          for (let i = 0; i !== s.length; ++i) {
            buf[i] = s.charCodeAt(i) & 0xff;
          }
          return buf;
        }
      }

      var workbook = XLSX.utils.book_new();

      // var table = document.querySelector('.excel-main table.el-table__body').cloneNode(true);
      var table = this.$refs.table.$el
        .querySelector("table.el-table__body")
        .cloneNode(true);
      // var thead = document.querySelector('.excel-main table.el-table__header thead').cloneNode(true);
      var thead = this.$refs.table.$el
        .querySelector("table.el-table__header thead")
        .cloneNode(true);
      // var footer = document.querySelector('.excel-main table.vxe-table--footer tfoot').cloneNode(true);

      while (thead.querySelector("th.gutter")) {
        thead.querySelector("th.gutter").remove();
      }

      var cc = 0;
      thead.querySelectorAll("th[colspan]").forEach(element => {
        // console.log(element)
        cc += Number.parseInt(element.getAttribute("colspan"));
      });
      cc +=
        thead.querySelector("tr").querySelectorAll("th").length -
        thead.querySelectorAll("th[colspan]").length;

      while (thead.querySelector("th.excel-hidden")) {
        thead.querySelector("th.excel-hidden").remove();
      }
      while (table.querySelector("td.excel-hidden")) {
        table.querySelector("td.excel-hidden").remove();
      }
      // let headContentStr=`销项发票明细表`
      // // var headContentStr = document.getElementById('peizaidantitle').innerHTML + document.getElementById('peizaidan').innerHTML;
      // // headContentStr = headContentStr.replace(/(\<\/span\>)(\<span\s+)/g, '$1&nbsp;&nbsp;&nbsp;$2').replace(/(\<\/div\>)(\<div\s+)/g, '$1<br/>$2')
      // var headContent = parseElement(`<tr><th colspan='${cc}'>${headContentStr}</th></tr>`);
      // thead.insertBefore(headContent, thead.querySelector('tr'));
      table.insertBefore(thead, table.querySelector("tbody"));
      // table.appendChild(footer);
      var sheet = XLSX.utils.table_to_sheet(table, { raw: true });
      // document.getElementById('pzd-title').innerText.replace(/\//g, '_')
      XLSX.utils.book_append_sheet(workbook, sheet, "我的申请");
      formatCell(sheet, /^[A-Z]+1$/g);

      // sheet['!rows'] = [{hpx: 250}]
      // console.log(sheet)

      sheet["!rows"] = [{ hpt: 65 }];

      lineHeight(sheet, true);

      // workbook.finalize();
      var wbOut = XLSXStyle.write(workbook, {
        bookType: "xlsx",
        bookSST: false,
        type: "binary"
      });

      saveAs(
        new Blob([s2ab(wbOut)], { type: "application/octet-stream" }),
        `我的申请明细表.xlsx`
      );
    },
    isShowByCode(code) {
      return this.specialCode.indexOf(code) === -1
    },
    cancelProcess(id) {
      this.$prompt('请输入审批撤销原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        if (!value) {
            this.$message({
              showClose: true,
              type: 'error',
              message: '请输入撤销原因'
            });
            return
        }
         const loading = this.$loading({
            lock: true,
            text: '撤回中，请稍后！',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
        cancelApply(id, value).then((res) => {
          if (res.resultCode == '0') {
            this.getPage()
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: res.resultMsg||'撤回失败'
            });
          }

        }).finally(() => {
          loading.close()
        })
      })
    },
    showshenpi(id, name) {
      // console.log(id)
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    replayProcess({ id, flowCode, flowName }) {
      let defPage = 'SubmitProcess'
      let defType
      let defJian
      processDetails(flowCode).then(res => {
        if (res && res.length > 0) {
          if (res[0].spare3) {
            defPage = res[0].spare3
          }
          if (res[0].spare4) {
            defType = res[0].spare4
          }
          if (res[0].spare5) {
            defJian = res[0].spare5
          }
        }
        // console.log(defPage)
        var path = '/process/' + defPage
        this.$router.push({ path: path, query: { title: flowName, code: flowCode, type: defType, processJian: defJian, relyProcessId: id }})
      })

      // this.$router.push({
      //   path: '/workflow/process/replay',
      //   query: {
      //     id: id
      //   }
      // })
    },
    refresh() {
      // 审批完成后会调用刷新方法
    },
    handlerGlobalParams(globalParams) {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      console.log(globalParams)
    },
    handleSizeChange(v) {
      this.quary.pageSize = v;
      this.quary.pageNum = 1;
      this.getPage();
    },
    eventPage(e) {
      this.getPage()
    },
    changemeun(code='PrepaymentReimbursement') {
      this.quary.name = code
      console.log(code)
      this.getPage()
    },

    getPage() {
      this.tableData = []
      // this.total = 0
      console.log(this.quary)
      getMyprocess(this.quary).then(res => {
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.page || []
          this.total = res.total || 0
          let tids = ''
          // 确保每个流程都有invoiceSum字段，如果没有则设为0
          this.tableData.forEach(item => {
            if (item.invoiceSum === undefined || item.invoiceSum === null) {
              item.invoiceSum = 0
            }
            tids += item.id + ','
          })
          if (tids) {
            tids = tids.slice(0, -1)
            getInvoiceCountByProcessIds(tids).then(res => {
              if (res && res.resultCode === '0') {
                res.data.forEach(item => {
                  const target = this.tableData.find(t => t.id === item.processId)
                  if (target) {
                    target.invoiceSum = item.count
                  }
                })
                this.tableData = this.tableData.map(item => {
                  return {
                    ...item,
                    invoiceSum: item.invoiceSum || 0
                  }
                })
                // this.tableData = this.tableData.slice()
              }
            })
          }
        } else {
          this.total = 0
          this.tableData = []
        }
      }).catch(err => {
        console.error('获取流程列表失败:', err)
        this.total = 0
        this.tableData = []
      })
    },
    ordercompanynameFmt(row, column, cellValue, index) {
      if (!cellValue|| row.flowCode=='paymentApprove') {
        return '--'
      }
      // for (var a = 0; a < this.ExternalAccount.length; a++) {
      //   var item = this.ExternalAccount[a]
      //   if (item.id === Number(cellValue)) {
      //     return item.companyName
      //   }
      // }
      return this.ExternalAccount[cellValue] ? this.ExternalAccount[cellValue] : cellValue
      // return cellValue
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == '') {
        return ' '
      }
      return dayjs(cellValue).format(fmtstr)
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    moneyFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = currency(cellValue, { symbol: '', precision: 2 }).format()
      }
      return v
    },
    // 表格中金额格式化方法
    moneyTableFmt(row, column, cellValue, index) {
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        return currency(cellValue, { symbol: '', precision: 2 }).format()
      }
      return '--'
    },
    // 单独的金额格式化方法，用于模板中
    formatMoney(value) {
      if (value != undefined && value != null && value != '') {
        return currency(value, { symbol: '', precision: 2 }).format()
      }
      return '--'
    },

    onQuery() {
      if (
        this.quary.shipTime !== undefined &&
        this.quary.shipTime !== null
      ) {
        this.quary.startTime = this.quary.shipTime[0]
        this.quary.endTime = this.quary.shipTime[1]
      } else {
        this.quary.startTime = undefined
        this.quary.endTime = undefined
      }

      this.quary.pageNum = 1
      this.getPage()
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath)
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath)
    },

    // 发票上传相关方法
    upload(row) {
      this.currentProcess = row
      this.invoiceDialogVisible = true
      this.fileList = []
      this.ocrResults = []
      this.existingInvoices = []
      // 查询已有发票
      this.loadExistingInvoices(row.id)
    },

    // 查询已有发票
    loadExistingInvoices(processId) {
      if (!processId) {
        console.warn('processId为空，无法查询发票')
        this.invoiceLoading = false
        return
      }
      this.uploadedAmount = 0

      this.invoiceLoading = true
      console.log('开始查询发票，processId:', processId)

      // API参数名是ids，传递processId
      listPrepaymentReimbursementInvoice(processId).then(res => {
        console.log('查询发票API返回结果:', res)

        // 检查返回结果的结构
        if (res && res.resultCode === '0') {
          // 处理返回的发票数据
          const invoiceData = res.list || []
          console.log('原始发票数据:', invoiceData)

          if (Array.isArray(invoiceData) && invoiceData.length > 0) {
            this.existingInvoices = invoiceData.map(invoice => ({
              id: invoice.id,
              fileName: invoice.title || invoice.fileName || '发票文件', // 发票内容
              imageUrl: invoice.invoiceUrl || invoice.originFile || invoice.imageUrl, // 发票图片地址
              invoiceCode: invoice.invoiceCode || '',
              invoiceNumber: invoice.invoiceNo || invoice.invoiceNumber || '',
              totalAmount: invoice.invoicePriceTaxSum || invoice.totalAmount || '',
              invoiceDate: invoice.invoiceDate || '',
              sellerName: invoice.sellerName || '',
              buyerName: invoice.buyerName || '',
              taxAmount: invoice.invoicePriceTax || invoice.taxAmount || '',
              pretaxAmount: invoice.invoicePrice || invoice.pretaxAmount || '', // 不含税金额
              invoiceDrawer: invoice.invoiceDrawer || '', // 开票人
              invoiceReview: invoice.invoiceReview || '', // 复核人
              invoiceType: invoice.invoiceType || '', // 发票类型
              // 保存原始数据用于编辑
              originalData: invoice
            }))

            console.log('处理后的发票列表:', this.existingInvoices)

            // 计算已有发票总金额
            this.uploadedAmount += this.existingInvoices.reduce((sum, invoice) => {
              return sum + (Number(invoice.totalAmount) || 0)
            }, 0)

            // 更新发票数量显示
            if (this.currentProcess) {
              this.currentProcess.invoiceSum = this.existingInvoices.length
            }

            // this.$message.success(`加载了 ${this.existingInvoices.length} 张已有发票`)
          } else {
            this.existingInvoices = []
            console.log('发票数据为空数组或不是数组:', invoiceData)
          }
        } else {
          this.existingInvoices = []
          console.log('查询发票API返回失败:', res)
          if (res && res.resultMsg) {
            this.$message.warning(`查询发票失败: ${res.resultMsg}`)
          }
        }
      }).catch(err => {
        console.error('查询发票API调用失败:', err)
        this.$message.error('查询发票失败: ' + (err.message || '网络错误'))
        this.existingInvoices = []
      }).finally(() => {
        this.invoiceLoading = false
      })
    },

    handleInvoiceDialogClose() {
      this.invoiceDialogVisible = false
      this.currentProcess = null
      this.fileList = []
      this.ocrResults = []
      this.existingInvoices = []
      this.uploadedAmount = 0
    },

    // 处理编辑发票对话框关闭
    handleEditInvoiceDialogClose(done) {
      this.editInvoiceDialogVisible = false
      this.isEditingOcrResult = false
      this.editingOcrIndex = -1
      // 重置表单数据
      this.editInvoiceForm = {
        id: '',
        title: '',
        invoiceCode: '',
        invoiceNo: '',
        invoiceDate: '',
        invoiceType: '',
        invoicePriceTaxSum: '',
        invoicePriceTax: '',
        invoicePrice: '',
        sellerName: '',
        buyerName: '',
        invoiceDrawer: '',
        invoiceReview: ''
      }
      // 如果是通过before-close调用的，需要调用done函数
      if (typeof done === 'function') {
        done()
      }
    },

    // 格式化日期以适配日期选择器
    formatDateForPicker(dateStr) {
      if (!dateStr) return ''

      try {
        // 处理各种可能的日期格式
        let formattedDate = dateStr

        // 如果是时间戳
        if (/^\d{13}$/.test(dateStr)) {
          formattedDate = dayjs(Number.parseInt(dateStr)).format('YYYY-MM-DD')
        }
        // 如果是10位时间戳
        else if (/^\d{10}$/.test(dateStr)) {
          formattedDate = dayjs(Number.parseInt(dateStr) * 1000).format('YYYY-MM-DD')
        }
        // 如果是标准日期格式 YYYY-MM-DD
        else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
          formattedDate = dateStr
        }
        // 如果是 YYYY/MM/DD 格式
        else if (/^\d{4}\/\d{2}\/\d{2}$/.test(dateStr)) {
          formattedDate = dateStr.replace(/\//g, '-')
        }
        // 如果是 YYYY年MM月DD日 格式
        else if (/^\d{4}年\d{1,2}月\d{1,2}日$/.test(dateStr)) {
          const match = dateStr.match(/^(\d{4})年(\d{1,2})月(\d{1,2})日$/)
          if (match) {
            const year = match[1]
            const month = match[2].padStart(2, '0')
            const day = match[3].padStart(2, '0')
            formattedDate = `${year}-${month}-${day}`
          }
        }
        // 如果是 YYYYMMDD 格式
        else if (/^\d{8}$/.test(dateStr)) {
          const year = dateStr.substring(0, 4)
          const month = dateStr.substring(4, 6)
          const day = dateStr.substring(6, 8)
          formattedDate = `${year}-${month}-${day}`
        }
        // 其他格式尝试用dayjs解析
        else {
          const parsed = dayjs(dateStr)
          if (parsed.isValid()) {
            formattedDate = parsed.format('YYYY-MM-DD')
          } else {
            console.warn('无法解析的日期格式:', dateStr)
            return ''
          }
        }

        // 验证最终格式是否正确
        if (!/^\d{4}-\d{2}-\d{2}$/.test(formattedDate)) {
          console.warn('日期格式化后仍不正确:', formattedDate)
          return ''
        }

        return formattedDate
      } catch (error) {
        console.error('日期格式化失败:', error, dateStr)
        return ''
      }
    },

    // 格式化数值以适配输入框显示
    formatNumberForInput(value) {
      if (!value && value !== 0) return ''

      try {
        // 如果是字符串，先转换为数字
        let numValue = value
        if (typeof value === 'string') {
          // 移除可能的千分位分隔符和货币符号
          numValue = value.replace(/[,￥$]/g, '')
          numValue = Number.parseFloat(numValue)
        }

        // 检查是否为有效数字
        if (isNaN(numValue)) {
          console.warn('无效的数值:', value)
          return ''
        }

        // 保留两位小数并转换为字符串
        return numValue.toFixed(2)
      } catch (error) {
        console.error('数值格式化失败:', error, value)
        return ''
      }
    },

    // 查看发票图片
    viewInvoiceImage(invoice) {
      if (invoice.imageUrl) {
        this.previewImageUrl = invoice.imageUrl
        this.previewVisible = true
      } else {
        this.$message.warning('该发票没有图片文件')
      }
    },

    // 编辑已有发票
    editExistingInvoice(invoice) {
      // 设置编辑状态
      this.isEditingOcrResult = false
      this.editingOcrIndex = -1

      // 填充编辑表单数据
      this.editInvoiceForm = {
        id: invoice.id,
        title: invoice.fileName || '',
        invoiceCode: invoice.invoiceCode || '',
        invoiceNo: invoice.invoiceNumber || '',
        invoiceDate: this.formatDateForPicker(invoice.invoiceDate),
        invoiceType: invoice.invoiceType || '',
        invoicePriceTaxSum: this.formatNumberForInput(invoice.totalAmount),
        invoicePriceTax: this.formatNumberForInput(invoice.taxAmount),
        invoicePrice: this.formatNumberForInput(invoice.pretaxAmount),
        sellerName: invoice.sellerName || '',
        buyerName: invoice.buyerName || '',
        invoiceDrawer: invoice.invoiceDrawer || '',
        invoiceReview: invoice.invoiceReview || ''
      }

      // 显示编辑对话框
      this.editInvoiceDialogVisible = true
    },

    // 保存编辑的发票
    saveEditedInvoice() {
      this.editInvoiceLoading = true

      if (this.isEditingOcrResult) {
        // 编辑新识别的发票 - 直接更新本地数据
        this.saveEditedOcrResult()
      } else {
        // 编辑已有发票 - 调用API更新
        this.saveEditedExistingInvoice()
      }
    },

    // 保存编辑的OCR识别结果
    saveEditedOcrResult() {
      if (this.editingOcrIndex === -1 || this.editingOcrIndex >= this.ocrResults.length) {
        this.$message.error('编辑的发票信息不存在')
        this.editInvoiceLoading = false
        return
      }

      // 判断是否为专票
      const isSpecialInvoice = this.isSpecialInvoice(this.editInvoiceForm.invoiceType)

      // 更新OCR结果数据
      const updatedInvoice = {
        ...this.ocrResults[this.editingOcrIndex],
        invoiceTitle: this.editInvoiceForm.title,
        invoiceCode: this.editInvoiceForm.invoiceCode,
        invoiceNumber: this.editInvoiceForm.invoiceNo,
        invoiceDate: this.editInvoiceForm.invoiceDate,
        invoiceType: this.editInvoiceForm.invoiceType,
        totalAmount: this.editInvoiceForm.invoicePriceTaxSum,
        taxAmount: isSpecialInvoice ? this.editInvoiceForm.invoicePriceTax : 0, // 只有专票才保存税额
        pretaxAmount: this.editInvoiceForm.invoicePrice,
        sellerName: this.editInvoiceForm.sellerName,
        buyerName: this.editInvoiceForm.buyerName,
        invoiceDrawer: this.editInvoiceForm.invoiceDrawer,
        invoiceReview: this.editInvoiceForm.invoiceReview
      }

      // 更新数组中的数据
      this.$set(this.ocrResults, this.editingOcrIndex, updatedInvoice)

      this.$message.success('发票信息更新成功')
      this.editInvoiceDialogVisible = false
      this.editInvoiceLoading = false
      this.isEditingOcrResult = false
      this.editingOcrIndex = -1
    },

    // 保存编辑的已有发票
    saveEditedExistingInvoice() {
      // 判断是否为专票
      const isSpecialInvoice = this.isSpecialInvoice(this.editInvoiceForm.invoiceType)

      // 构造更新数据
      const updateData = {
        id: this.editInvoiceForm.id,
        title: this.editInvoiceForm.title,
        invoiceCode: this.editInvoiceForm.invoiceCode,
        invoiceNo: this.editInvoiceForm.invoiceNo,
        invoiceDate: this.editInvoiceForm.invoiceDate,
        invoiceType: this.editInvoiceForm.invoiceType,
        invoicePriceTaxSum: this.editInvoiceForm.invoicePriceTaxSum,
        invoicePriceTax: isSpecialInvoice ? this.editInvoiceForm.invoicePriceTax : 0, // 只有专票才保存税额
        invoicePrice: this.editInvoiceForm.invoicePrice,
        sellerName: this.editInvoiceForm.sellerName,
        buyerName: this.editInvoiceForm.buyerName,
        invoiceDrawer: this.editInvoiceForm.invoiceDrawer,
        invoiceReview: this.editInvoiceForm.invoiceReview
      }

      console.log('更新已有发票数据:', updateData)

      // 调用更新发票的API
      updatePrepaymentReimbursementInvoice(updateData).then(res => {
        if (res && res.resultCode === '0') {
          this.$message.success('发票信息更新成功')
          this.editInvoiceDialogVisible = false
          // 重新加载发票列表
          this.loadExistingInvoices(this.currentProcess.id)
        } else {
          this.$message.error(res.resultMsg || '更新失败')
        }
      }).catch(err => {
        console.error('更新发票失败:', err)
        this.$message.error('更新失败: ' + (err.message || '未知错误'))
      }).finally(() => {
        this.editInvoiceLoading = false
        this.isEditingOcrResult = false
        this.editingOcrIndex = -1
      })
    },

    // 删除发票
    deleteInvoice(invoice) {
      this.$confirm('确定要删除这张发票吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '删除中，请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 调用删除接口
        delInvoiceDialog([invoice.id]).then(res => {
          if (res && res.resultCode === '0') {
            // 从列表中移除
            const index = this.existingInvoices.findIndex(item => item.id === invoice.id)
            if (index > -1) {
              this.existingInvoices.splice(index, 1)
            }

            // 更新发票数量显示
            if (this.currentProcess) {
              this.currentProcess.invoiceSum = this.existingInvoices.length
            }

            // 刷新主列表中的发票数量
            this.getPage()

            this.$message.success('删除成功')
          } else {
            this.$message.error(res.resultMsg || '删除失败')
          }
        }).catch(err => {
          console.error('删除发票失败:', err)
          this.$message.error('删除失败: ' + (err.message || '未知错误'))
        }).finally(() => {
          loading.close()
        })
      }).catch(() => {
        // 用户取消删除
      })
    },

    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(file.type)
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('只支持jpg、jpeg、png、pdf格式的文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('文件大小不能超过5MB!')
        return false
      }
      return true
    },

    handleUploadSuccess(response, file, fileList) {
      console.log('上传成功:', response)
      if (response && response.url) {
        // 自动调用OCR识别
        this.performOCR(response.url, file.name)
      }
    },

    handleUploadError(err, file, fileList) {
      console.error('上传失败:', err)
      this.$message.error('文件上传失败，请重试!')
    },

    handleRemove(file, fileList) {
      // 移除对应的OCR结果
      const fileName = file.name
      this.ocrResults = this.ocrResults.filter(item => item.fileName !== fileName)
    },

    handlePreview(file) {
      if (file.url) {
        this.previewImageUrl = file.url
        this.previewVisible = true
      }
    },

    // OCR识别方法
    performOCR(imageUrl, fileName) {
      this.ocrLoading = true
      recognizeOcrByImgurl(imageUrl).then(res => {
        console.log('OCR识别结果:', res)
        if (res) {
          try {
            const ocrData = JSON.parse(res)
            const results = this.processOcrResults(ocrData, imageUrl, fileName)
            if (results && results.length > 0) {
              console.log('1384',results)
              this.uploadedAmount += results.reduce((sum, invoice) => {
                return sum + (Number(invoice.totalAmount) || 0)
              }, 0)
              this.ocrResults.push(...results)
              this.$message.success(`${fileName} 识别完成，识别到 ${results.length} 张发票`)
            } else {
              this.$message.warning(`${fileName} 识别失败，请手动输入发票信息`)
            }
          } catch (e) {
            console.error('OCR结果解析失败:', e)
            this.$message.warning(`${fileName} 识别结果解析失败，请手动输入发票信息`)
          }
        } else {
          this.$message.warning(`${fileName} 识别失败，请手动输入发票信息`)
        }
      }).catch(err => {
        console.error('OCR识别失败:', err)
        this.$message.error(`${fileName} 识别失败: ${err.message || '未知错误'}`)
      }).finally(() => {
        this.ocrLoading = false
      })
    },

    // 处理OCR识别结果，参考submitprocess的实现
    processOcrResults(ocrData, fileUrl, fileName) {
      const results = []
      if (ocrData.MixedInvoiceItems) {
        ocrData.MixedInvoiceItems.forEach((item, index) => {
          const invoice = this.extractInvoiceInfo(item.SingleInvoiceInfos[item.SubType], fileUrl, item.SubType, fileName, index)
          if (invoice) {
            results.push({
              ...invoice,
              fileName: fileName,
              imageUrl: fileUrl,
              invoiceCode: invoice.invoiceCode || '',
              invoiceNumber: invoice.invoiceNumber || '',
              totalAmount: invoice.totalAmount || '',
              invoiceDate: invoice.invoiceDate || '',
              sellerName: invoice.sellerName || '',
              buyerName: invoice.buyerName || '',
              taxAmount: invoice.taxAmount || 0, // 税额，普票为0
              pretaxAmount: invoice.pretaxAmount || '',
              invoiceType: item.SubType || '', // 使用SubType作为发票类型
              invoiceTypeName: item.TypeDescription || '',
              invoiceTitle: invoice.invoiceTitle || '', // 发票内容/抬头
              invoiceDrawer: invoice.invoiceDrawer || '', // 开票人
              invoiceReview: invoice.invoiceReview || '', // 复核人
              originalData: item
            })
          }
        })
      }
      return results
    },

    // 提取发票信息，参考submitprocess的invoiceOcr方法
    extractInvoiceInfo(item, fileUrl, invoiceType, fileName, index) {
      if (!item) return null

      const invoice = {}

      // 发票代码
      if (item.InvoiceCode || item.Code) {
        invoice.invoiceCode = item.InvoiceCode || item.Code
      }

      // 发票号码
      if (item.InvoiceNum || item.Number || item.ReceiptNumber) {
        invoice.invoiceNumber = item.InvoiceNum || item.Number || item.ReceiptNumber
      }

      // 开票日期
      if (item.InvoiceDate || item.Date) {
        invoice.invoiceDate = item.InvoiceDate || item.Date
      }

      // 价税合计
      if (item.TotalAmount || item.Total || item.Fare) {
        invoice.totalAmount = item.TotalAmount || item.Total || item.Fare
      }

      // 税额 - 只有专票才提取税额
      const specialInvoiceTypes = ['VatSpecialInvoice', 'VatElectronicSpecialInvoice', 'VatElectronicSpecialInvoiceFull']
      const isSpecialInvoice = specialInvoiceTypes.includes(invoiceType)

      if (isSpecialInvoice && (item.TotalTax || item.Tax || item.TaxAmount)) {
        invoice.taxAmount = item.TotalTax || item.Tax || item.TaxAmount
      } else {
        // 普票税额设为0
        invoice.taxAmount = 0
      }

      // 不含税金额 - 优化计算逻辑
      if (item.PretaxAmount) {
        invoice.pretaxAmount = item.PretaxAmount
      } else if (invoice.totalAmount && invoice.taxAmount && isSpecialInvoice) {
        // 只有专票才用税额计算不含税金额
        invoice.pretaxAmount = currency(invoice.totalAmount).subtract(invoice.taxAmount).value
      } else if (invoice.totalAmount) {
        // 普票或没有税额时，不含税金额等于总金额
        invoice.pretaxAmount = invoice.totalAmount
      }

      // 销售方
      if (item.SellerName || item.Seller) {
        invoice.sellerName = item.SellerName || item.Seller
      }

      // 购买方
      if (item.PurchaserName || item.BuyerName || item.Buyer) {
        invoice.buyerName = item.PurchaserName || item.BuyerName || item.Buyer
      }

      // 发票内容/抬头 - 优化title字段
      if (item.Title || item.Name) {
        invoice.invoiceTitle = item.Title || item.Name
      } else if (item.VatInvoiceItemInfos && item.VatInvoiceItemInfos.length > 0) {
        // 从发票明细中提取商品名称作为发票内容
        invoice.invoiceTitle = item.VatInvoiceItemInfos.map(detail => detail.Name).join(',')
      } else if (item.VatElectronicItems && item.VatElectronicItems.length > 0) {
        invoice.invoiceTitle = item.VatElectronicItems.map(detail => detail.Name).join(',')
      } else if (item.GeneralMachineItems && item.GeneralMachineItems.length > 0) {
        invoice.invoiceTitle = item.GeneralMachineItems.map(detail => detail.Name).join(',')
      }

      // 开票人
      if (item.Issuer || item.InvoiceDrawer) {
        invoice.invoiceDrawer = item.Issuer || item.InvoiceDrawer
      }

      // 复核人
      if (item.Reviewer || item.InvoiceReview) {
        invoice.invoiceReview = item.Reviewer || item.InvoiceReview
      }

      invoice.BuyerTaxID = item.BuyerTaxID
      invoice.SellerTaxID = item.SellerTaxID

      return invoice
    },

    editOcrResult(row) {
      // 编辑OCR识别结果 - 使用完整的编辑对话框
      const index = this.ocrResults.findIndex(item => item === row)
      if (index === -1) {
        this.$message.error('找不到要编辑的发票信息')
        return
      }

      // 设置编辑状态
      this.isEditingOcrResult = true
      this.editingOcrIndex = index

      // 填充编辑表单数据
      this.editInvoiceForm = {
        id: '', // OCR结果没有ID
        title: row.invoiceTitle || row.fileName || '',
        invoiceCode: row.invoiceCode || '',
        invoiceNo: row.invoiceNumber || '',
        invoiceDate: this.formatDateForPicker(row.invoiceDate),
        invoiceType: row.invoiceType || '',
        invoicePriceTaxSum: this.formatNumberForInput(row.totalAmount),
        invoicePriceTax: this.formatNumberForInput(row.taxAmount),
        invoicePrice: this.formatNumberForInput(row.pretaxAmount),
        sellerName: row.sellerName || '',
        buyerName: row.buyerName || '',
        invoiceDrawer: row.invoiceDrawer || '',
        invoiceReview: row.invoiceReview || ''
      }

      // 显示编辑对话框
      this.editInvoiceDialogVisible = true
    },
    saveFun(fn){
      // 计算新上传发票总金额

      this.uploadLoading = true

      // 构造保存数据 - 只保存新识别的发票
      const invoiceList = this.ocrResults.map(item => {
        // 判断是否为专票
        const isSpecialInvoice = this.isSpecialInvoice(item.invoiceType)

        return {
          // 基础信息
          title: item.invoiceTitle || item.fileName, // 优化：优先使用发票内容，其次使用文件名
          invoiceUrl: item.imageUrl, // 发票图片地址
          originFile: item.imageUrl, // 发票原始文件地址

          // 发票识别信息
          invoiceCode: item.invoiceCode, // 发票代码
          invoiceNo: item.invoiceNumber, // 发票号码
          invoiceDate: item.invoiceDate, // 开票日期
          invoiceType: item.invoiceType, // 发票类型 - 使用SubType

          // 金额信息 - 优化税额处理
          invoicePriceTaxSum: item.totalAmount, // 价税合计
          invoicePriceTax: isSpecialInvoice ? item.taxAmount : 0, // 税额：只有专票才有税额，普票为0
          invoicePrice: item.pretaxAmount, // 不含税金额

          // 开票方和收票方信息
          sellerName: item.sellerName, // 销售方名称
          buyerName: item.buyerName, // 购买方名称
          buyerTaxpayerNo: item.BuyerTaxID, // 购买方纳税人识别号
          sellerTaxpayerNo: item.SellerTaxID, // 销售方纳税人识别号

          // 发票人员信息
          invoiceDrawer: item.invoiceDrawer, // 开票人
          invoiceReview: item.invoiceReview, // 复核人

          // 流程关联信息
          associatedProcessId: this.currentProcess.id,
          invoiceDataSource: 'OCR识别',
          isItRepeated: 0,
          isCertificate: 0,
          delFlag: 0
        }
      })

      console.log('保存发票数据:', invoiceList)

      // 调用保存发票的API
      addPrepaymentReimbursementInvoice(invoiceList).then(res => {
        console.log('保存发票结果:', res)
        if (res && res.resultCode === '0') {
          this.$message.success('发票保存成功')

          // 重新加载已有发票列表
          this.loadExistingInvoices(this.currentProcess.id)

          // 清空新识别的发票列表
          this.ocrResults = []
          this.fileList = []

          // 刷新主列表中的发票数量
          this.getPage()
          fn && fn()
        } else {
          this.$message.error(res.resultMsg || '保存失败')
        }
      }).catch(err => {
        console.error('保存发票失败:', err)
        this.$message.error('保存失败: ' + (err.message || '未知错误'))
      }).finally(() => {
        this.uploadLoading = false
      })
    },

    saveInvoices() {
      if (this.ocrResults.length === 0) {
        this.$message.warning('没有新的发票需要保存')
        return
      }
      if(this.uploadedAmount != this.currentProcess.globalParam3){
        // 提示 是否继续
        this.$confirm('开票金额与申请预付款金额不符，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 继续保存
          this.saveFun()
        }).catch(() => {
          // 取消保存
          return
        })
        // this.$message.warning('开票金额与申请预付款金额不符')
        return
      }
      this.saveFun(()=>{
        this.handleInvoiceDialogClose()
      })


    }
  }
}
</script>

<style scoped>
.input-different{
  width: 200px;
  margin-left:60px;

}
.font{
  font-size:14px;
  color:#7E7E7E;
}
.el-table-info >>> .cell{
  text-align: center;
}
.el-table-info >>> th {
  background: #EDF5FF;
}
.mytable td  {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.mytable td .cell {
  padding-left: 0.1rem;
  padding-right: 0.1rem;
}
.el-table-info >>> .warning-cell{
  color: red;
}
.el-table-info >>> .success-cell{
  color: #6DD400;
}
.pointer{
  cursor: pointer;
color:#1890ff;
}
</style>
