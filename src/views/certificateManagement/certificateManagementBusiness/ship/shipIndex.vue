<template>
  <section>
    <div class="app-container">
      <AccModel @updateAccModel="updateAccModel"></AccModel>
    <el-form ref="queryForm" :inline="true">
      <!-- <el-form-item label="状态">
        <el-tag style="margin-right: 10px;cursor: pointer;" :type="applyStatus==item.code?'success':'info'"
                v-for="(item,index) in  applyStatusList" :key="index" @click="applyStatusQuery(item)"
        >{{item.value}}</el-tag>
      </el-form-item> -->
      <el-form-item label="选择日期">
          <el-date-picker
            v-model="queryParams.creditTime"
            placeholder="选择日期"
            type="month"
            value-format="yyyy-MM"
            clearable
            size="small"
          />
        </el-form-item>
      <el-form-item label="船舶名称">
        <el-input
          v-model="queryParams.shipName"
          placeholder="船舶名称"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <!-- <el-form-item :label="showTimeTittle+'日期'">
        <el-date-picker
          v-model="queryParams.shipLiGangTime"
          :placeholder="showTimeTittle+'日期'"
          type="date"
          value-format="yyyy-MM-dd"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="航次号">
        <el-input
          v-model="queryParams.voyageNumber"
          placeholder="航次号"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="挂帐号">
        <el-input
          v-model="queryParams.onAccountNum"
          placeholder="挂帐号"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item> -->
      <!-- <el-form-item v-if="false" label="船期">
        <el-date-picker
          v-model="queryParams.shipTime"
          placeholder="船期"
          type="date"
          value-format="yyyy-MM-dd"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="类型">
        <el-tag style="margin-right: 10px;cursor: pointer;" :type="typeAccountSelect==item.value?'success':'info'"
                v-for="(item,index) in  typeAccountList" :key="index" @click="applyTypeQuery(item)"
        >{{item.label}}</el-tag>
      </el-form-item> -->
      <!-- <div> -->
        <el-button  @click="topage(1)" type="primary">查询</el-button>
      <!-- </div> -->
    </el-form>
      <!-- style="width: 100%;margin-top: 30px"
    :show-overflow-tooltip="true"
    border -->
  <!-- <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
      :buttons="toolbarButtons"
    >
    <template #buttons>
      <vxe-button status="primary" @click="downloadTemplate(pzdExcelUrl)">下载模版</vxe-button>
      <vxe-button status="success" @click="openUploadInvoce(1)">导入</vxe-button>
    </template>
  </vxe-toolbar> -->
  <vxe-table
  ref="xTable"
    class="el-table-info"
    :data="tableData"
      stripe
      size="small"
      border auto-resize
      align="center"
      :print-config="{}"
      max-height="800"
      highlight-current-row
      :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '挂账台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
      resizable

  >
    <vxe-table-column
      type="seq"
      title="#"
      width="40"
    >
    </vxe-table-column>
   <vxe-table-column
      :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'YYYY-MM')}"
      field="creditTime"
      title="挂账时间"
    >
    </vxe-table-column>
 <!-- <vxe-table-column
      :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"
      field="onAccountNum"
      title="挂账号"
    >
    </vxe-table-column> -->

    <!-- <vxe-table-column
      :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"
      field="epartureTimeDate"
      :title="showTimeTittle+'日期'"
    >
    </vxe-table-column> -->


    <vxe-table-column
      field="voyageNumber"
      title="航次号"
    >
    </vxe-table-column>
    <vxe-table-column
      :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"
      field="shipNameStr"
      title="船舶名称"
    >
    </vxe-table-column>
     <!-- <vxe-table-column
      field="shipTime"
      :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"
      title="船期"
    >
    </vxe-table-column> -->
    <!-- <vxe-table-column
      :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"
      field="createDate"
      title="创建时间"
    >
    </vxe-table-column> -->
    <!-- 挂帐状态 -->
     <vxe-table-column
      title="凭证状态"
    >
      <template slot-scope="scope">
        <span v-if="scope.row[codeListKey] && scope.row[codeListCount] > 0"">已生成</span>
        <span v-else style="color:#f25584;">未生成</span>
      </template>
    </vxe-table-column>
    <vxe-table-column
      title="操作"
    >
      <template slot-scope="scope">
        <el-button  type="text" size="small" @click="openhe(scope.row)">查看</el-button>
        <el-button v-if="scope.row[codeListKey] && scope.row[codeListCount]==0" :class="{btnCol:isCertModel=='2'}" type="text" size="small" @click="showYacc(scope.row,codeKey)">生成凭证</el-button>
        <el-button v-if="scope.row[codeListKey] && scope.row[codeListCount] > 0"  :class="{btnCol:isCertModel=='2'}" type="text" size="small" @click="showTranByCode(scope.row,codeKey)">查看凭证</el-button>
        <!-- <el-button  v-if="scope.row[codeListKeyYF] && scope.row[codeListCountYF]==0"  :class="{btnCol:isCertModel=='2'}" type="text" size="small" @click="showYacc(scope.row,codeKeyYF)">生成应付凭证</el-button> -->
        <!-- <el-button  v-if="scope.row[codeListKeyYF] && scope.row[codeListCountYF]>0" :class="{btnCol:isCertModel=='2'}"  type="text" size="small" @click="showTranByCode(scope.row,codeKeyYF)">查看应付凭证</el-button> -->
        <!-- <el-button  type="text" size="small">
          <router-link :to="{path:'/OnAccount/OnAccount/stowage/'+scope.row.id,query:{pid:scope.row.processId,deptCode}}" >
            查看配载单
          </router-link>
        </el-button>
        <el-button  type="text" size="small" @click="showProcessDetail(scope.row.processId)">查看审批详情</el-button> -->
      </template>
    </vxe-table-column>
  </vxe-table>
  <el-pagination
    background
    layout="prev, pager, next"
    :total="total"
    :page-size="pageSize"
    :current-page="pageNo"
    @current-change="eventPage"
    style="text-align: right;padding: 10px 0px;background-color: #fff"
  >
  </el-pagination>

    <el-dialog
      title="费用合计"
      :visible.sync="showheji"
      top="0vh"
      width="100%"
      :before-close="handleCloseshowheji"
      append-to-body
    >
      <div id="total-cost">
        <div id="total-cost-print" >
          <div id="hangci-biaoti-id" class="hangci-biaoti" style="position: relative;display: flex;justify-content:space-between;">
            <div>
              <div>
                <span class="hangci-biaoti12"><span style="font-weight:bold;">船名：</span>{{shipLine.shipNameStr}} </span>
                <!-- <span class="hangci-biaoti12"><span style="font-weight:bold;">船名-{{showTimeTittle}}：</span>{{shipLine.shipNameStr}} - {{ shipLine.epartureTimeDate == null ? "": shipLine.epartureTimeDate.substring(0,10) }}</span> -->
                <span class="hangci-biaoti12">
                  <!-- <span style="font-weight:bold;">合同公司</span>：{{companyFmtn(shipLine.maiCompany||shipLine.contractCode)}}  -->
                </span>

              </div>
              <div style="margin-top:20px;">
                <span class="hangci-biaoti12"  ><span style="font-weight:bold;">货种：</span>{{goodsTypeNames||'--'}} </span>
                <!-- <span class="hangci-biaoti12"><span style="font-weight:bold;">挂账号：</span>{{publicFmtn(shipLine.onAccountNum)}} </span> -->
                <!-- <span class="hangci-biaoti12"><span style="font-weight:bold;">航次号：</span>{{publicFmtn(shipLine.voyageNumber)}}</span> -->
                <!-- <span class="hangci-biaoti12"><span style="font-weight:bold;">船东：</span>{{publicFmtn(name)}}</span> -->
              </div>
            </div>

            <div style="margin-left:20px;">
              <div>
                <span class="hangci-biaoti12"><span style="font-weight:bold;">航次号：</span>{{publicFmtn(shipLine.voyageNumber)}}</span>
                <!-- <span class="hangci-biaoti12"><span style="font-weight:bold;">整船收入：</span>【含税：{{priceFormat(taxIncomcaiwu)}} 】 【不含税：{{priceFormat(taxIncomcaiwushuino)}}】</span> -->
                <!-- <span v-if="Maifinancial" class="hangci-biaoti12"><span style="font-weight:bold;">票税成本：</span>{{priceFormat(Maifinancial)}}</span>
                <span v-if="Dianinterest" class="hangci-biaoti12"><span style="font-weight:bold;">垫资利息：</span>{{priceFormat(Dianinterest)}}</span> -->

              </div>
              <div style="margin-top:20px;">
                <span class="hangci-biaoti12"><span style="font-weight:bold;">起运港：</span>
                  <template >
                    {{publicFmtn(startPortName)}}
                  </template>
                </span>
                <span class="hangci-biaoti12" style="margin-left:20px"><span style="font-weight:bold;">目的港：</span>
                   <template >
                    {{publicFmtn(endPortName)}}
                   </template>
                </span>
                <!-- <span class="hangci-biaoti12" style="margin-left:20px"><span style="font-weight:bold;">装货码头：</span>
                    <template v-if="this.shipLine.id != '40c8bfb9eb974819a5b70a9ffa022f0b'">
                      {{publicFmtn(loadWhrafName)}}
                    </template>
                    <template v-else>
                      辽渔;丹东
                    </template>
                </span>
                <span class="hangci-biaoti12" style="margin-left:20px"><span style="font-weight:bold;">卸货码头：</span>
                   <template v-if="this.shipLine.id != '40c8bfb9eb974819a5b70a9ffa022f0b'">
                    {{publicFmtn(unloadWharfName)}}
                   </template>
                  <template v-else>
                      定安;定安
                  </template>
                </span> -->
              </div>
            </div>
            <div>
<!--              <div><span class="hangci-biaoti12" ><span style="font-weight:bold;">核算：</span>{{areaName||'--'}} </span></div> -->
              <div><span class="hangci-biaoti12" ></span></div>
              <div style="margin-top:20px;">
                <!-- <span class="hangci-biaoti12"  ><span style="font-weight:bold;">货种：</span>{{goodsTypeNames||'--'}} </span> -->
              </div>

            </div>
          </div>

          <!-- <div v-if="zonglist.length>0">
            <el-divider content-position="left">海运费收支</el-divider>
            <vxe-table
              class="el-table-info"
              :data="zonglist"
              stripe
              style="width: 100%;margin-top: 20px"
              border
            >
              <vxe-table-column field="name" :formatter="publicFmtVxe" title="类型"></vxe-table-column>
              <vxe-table-column field="priceno" :formatter="priceFormatFmt" title="不含税价"></vxe-table-column>
             <vxe-table-column field="price" :formatter="priceFormatFmt" title="含税价"></vxe-table-column>

            </vxe-table>
          </div> -->


          <el-divider content-position="left">收入
            <!-- &nbsp;&nbsp;<el-button id="mergeBtn" type="success" @click="mergeYufei" plain round size="mini">{{isMergeYunFei?'取消合并':'合并'}}</el-button> -->
            </el-divider>
          <vxe-table
            class="el-table-info"
            :data="yufeilist"
            stripe
            :loading="loadingHeZhi"
            :footer-method="footerMethod"
            show-footer
            ref="xTableYunfei"
            :merge-cells="mergeCells"
            style="width: 100%;margin-top: 20px"
            :show-overflow-tooltip="true"
            border
          >
            <vxe-table-column field="customerid" width="300" align="center" :formatter="publicFmtVxe" title="客户名称">
              <template slot-scope="scope">
                <div v-if="scope.row.officeName">{{ scope.row.officeName }}</div>
                <div v-if="!scope.row.officeName">{{scope.row.customerid}}</div>
              </template>
            </vxe-table-column>
            <vxe-table-column field="costtype" width="160" align="center" :formatter="publicFmtVxe" v-if="!isMergeYunFei" title="费用类型"></vxe-table-column>
            <vxe-table-column field="tax" width="100" align="center" :formatter="bill_taxFmtt" v-if="!isMergeYunFei" title="税率"></vxe-table-column>
            <vxe-table-column field="settleTong" width="200" align="center" :formatter="publicFmtVxe" title="吨位"></vxe-table-column>
            <vxe-table-column field="settlePrice" width="100" align="right" :formatter="publicFmtVxe" title="单价"></vxe-table-column>

            <!-- <vxe-table-column field="priceNo" :formatter="priceFormatFmt" title="金额不含税"></vxe-table-column> -->
            <vxe-table-column field="price" align="right" :formatter="priceFormatFmt" title="金额"></vxe-table-column>
            <vxe-table-column field="priceSum"  align="right" :formatter="priceFormatFmt" title="小计"></vxe-table-column>
          </vxe-table>
          <!-- <el-divider content-position="left" v-if="shipcostlistshou.length>0">船上费用收入</el-divider>
          <vxe-table
            class="el-table-info"
            :data="shipcostlistshou"
            v-if="shipcostlistshou.length>0"
            stripe
            ref="xTableShipCost"
            :footer-method="footerMethodShipCost"
            show-footer
            style="width: 100%;margin-top: 20px"
            :show-overflow-tooltip="true"
            border
          >
            <vxe-table-column field="sysSupplierId" align="center" :formatter="fmtsuName" title="供应商"></vxe-table-column>
            <vxe-table-column field="costProject" align="center" width="80" :formatter="items_occurredFmty" title="费用类型"></vxe-table-column>
            <vxe-table-column field="tax" align="center" :formatter="bill_taxFmtt" title="税率"></vxe-table-column>
            <vxe-table-column field="costPrice" align="center" :formatter="priceFormatFmt" title="金额不含税"></vxe-table-column>
          <vxe-table-column field="costPriceNo" align="center" :formatter="priceFormatFmt" title="金额"></vxe-table-column>

          </vxe-table> -->

          <!-- <div >
            <el-divider content-position="left">成本</el-divider>
            <vxe-table
              class="el-table-info"
              :data="shippaylist"
              ref="xTableShipPay"
              :footer-method="footerMethod"
            show-footer
              stripe
              :merge-cells="mergeShipPayCells"
              style="width: 100%;margin-top: 20px"
              border
            >
            <vxe-table-column field="customerid" align="center" width="300" :formatter="publicFmtVxe" title="供应商">
              <template slot-scope="scope">
                <div v-if="scope.row.officeName">{{ scope.row.officeName }}</div>
                <div v-if="!scope.row.officeName">{{scope.row.customerid}}</div>
              </template>
            </vxe-table-column>
            <vxe-table-column field="modelName" align="center" width="120" :formatter="publicFmtVxe"  title="类型"></vxe-table-column>
            <vxe-table-column field="costtype" align="center" width="160" :formatter="publicFmtVxe" v-if="!isMergeYunFei" title="费用类型"></vxe-table-column>
            <vxe-table-column field="tax" align="center" width="100" :formatter="bill_taxFmtt" title="税率"></vxe-table-column>
            <vxe-table-column field="settleTong" align="center" width="200" :formatter="publicFmtVxe" title="吨位"></vxe-table-column>
            <vxe-table-column field="settlePrice" align="right" width="100" :formatter="publicFmtVxe" title="单价"></vxe-table-column>
            <vxe-table-column field="price"  align="right" :formatter="priceFormatFmt" title="金额"></vxe-table-column>
              <vxe-table-column field="priceSum"  align="right" :formatter="priceFormatFmt" title="小计"></vxe-table-column>
            <vxe-table-column field="modelPriceSum"  align="right" :formatter="priceFormatFmt" title="类型小计"></vxe-table-column>
            </vxe-table>
          </div> -->
          <!-- <div v-if="ship10PriceList && ship10PriceList.length>0">
            <el-divider content-position="left">船方承担</el-divider>
            <vxe-table
              class="el-table-info"
              :data="ship10PriceList"
              ref="xTableShipPay10Tab"
              :footer-method="footerMethod"
            show-footer
              stripe
              style="width: 100%;margin-top: 20px"
              border
            >
            <vxe-table-column field="customerid" align="center" width="300" :formatter="publicFmtVxe" title="供应商">
              <template slot-scope="scope">
                <div style="display: flex;justify-content: space-evenly;width: 100%">
                  <div style="display: flex;">
                    <div v-if="scope.row.officeName">{{ scope.row.officeName }}</div>
                    <div v-if="!scope.row.officeName">{{scope.row.customerid}}</div>
                    <div style="color: red" v-if="scope.row.incomeCompany == 10">(网)</div>
                  </div>
                </div>
              </template>
            </vxe-table-column>
            <vxe-table-column field="costtype" align="center" width="160" :formatter="publicFmtVxe" v-if="!isMergeYunFei" title="费用类型"></vxe-table-column>
            <vxe-table-column field="tax" align="center" width="100" :formatter="bill_taxFmtt" title="税率"></vxe-table-column>
            <vxe-table-column field="settleTong" align="center" width="200" :formatter="publicFmtVxe" title="吨位"></vxe-table-column>
            <vxe-table-column field="settlePrice" align="right" width="100" :formatter="publicFmtVxe" title="单价"></vxe-table-column>
            <vxe-table-column field="price"  align="right" :formatter="priceFormatFmt" title="金额"></vxe-table-column>
            </vxe-table>
          </div> -->
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button  type="primary" size="small" @click="verifyPrintBefore()">打印</el-button>
        <el-button v-if="applyStatus === 1" type="primary" size="small" @click="onAccountFinish(shipLine.id,shipLine.processId,1)">生成凭证</el-button>
        <el-button v-if="applyStatus === 1" type="primary" size="small" @click="onAccountFinish(shipLine.id,shipLine.processId,0)">挂账退回</el-button>
      </div>
    </el-dialog>


    <el-dialog
      title="审批详情"
      :visible.sync="showProcess"
      width="50%"
      :before-close="handleClosesshowProcess"
      append-to-body
    >
      <el-divider content-position="left">审批流程</el-divider>
      <el-timeline id="timelineBody">
        <!-- 发起申请 -->
        <el-timeline-item
          :timestamp="timeline.start.content"
          color="#67C23A"
          size="large"
          placement="top">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
              <el-avatar shape="square" size="large" :src="timeline.start.item.avatar"></el-avatar>
              <div style="margin-left: 10px;color: #333333;">{{timeline.start.item.name}}</div>
            </div>
            <div style="display: flex;align-items: flex-end;flex-direction: column">
              <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{timeline.start.item.sponsorTime}}</div>
              <el-tooltip v-if="!!timeline.start.reason && timeline.start.reason.length > 22" effect="dark" placement="top">
                <div slot="content">
                  <div v-html="timeline.start.reason"></div>
                </div>
                <div style="font-size: 12px;color: #909399;cursor: default;">{{timeline.start.reason.substr(0,20)}}...</div>
              </el-tooltip>
              <div v-else style="font-size: 12px;color: #909399">{{timeline.start.reason}}</div>
            </div>
          </div>
        </el-timeline-item>

        <!-- 审批人 -->
        <el-timeline-item
          v-for="(activity, index) in timeline.sp"
          v-if="timeline.status != '4'"
          :timestamp="activity.content"
          :key="index"
          :color="activity.color"
          :size="activity.size"
          placement="top">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
              <!--                <el-avatar shape="square" size="large" :src="activity.item.avatar"></el-avatar>-->
              <el-avatar v-if="activity.item.sp_type=='0'" shape="square" size="large" :src="activity.item.avatar"></el-avatar>
              <span class="diy-avatar" v-if="activity.item.sp_type=='1'">{{activity.item.avatar|strSubFil}}</span>
              <!--                <div style="margin-left: 10px;color: #333333;">{{activity.item.name}}</div>-->
              <div style="margin-left: 10px;color: #333333;">{{activity.item.sp_type=='0'?activity.item.name:activity.item.avatar}}</div>
            </div>
            <div style="display: flex;align-items: flex-end;flex-direction: column">
              <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{activity.commentTime}}</div>
              <el-tooltip v-if="!!activity.comment && activity.comment.length > 22" effect="dark" placement="top">
                <div slot="content">
                  <div v-html="activity.comment"></div>
                </div>
                <div style="font-size: 12px;color: #909399;cursor: default;">{{activity.comment.substr(0,20)}}...</div>
              </el-tooltip>
              <div v-else style="font-size: 12px;color: #909399">{{activity.comment}}</div>
            </div>
          </div>
        </el-timeline-item>

        <!-- 抄送人 -->
        <el-timeline-item
          :timestamp="timeline.ccs.content"
          :color="timeline.ccs.color"
          :size="timeline.ccs.size"
          placement="top">
          <el-row class="timelineContent">
            <div v-for="people in timeline.ccs.items" style="margin-right:10px">
              <el-avatar shape="square" size="large" :src="people.avatar"></el-avatar>
            </div>
          </el-row>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>

      <!--打印操作历史-->
      <el-dialog title="打印历史" :visible.sync="isShowPrintHistory">
        <el-table :data="printHistoryData">
          <el-table-column property="createByName" label="打印人"></el-table-column>
          <el-table-column property="createTime" label="打印时间"></el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="isShowPrintHistory = false">取 消</el-button>
          <el-button type="primary" @click="onCloseValuePrint()">继续打印</el-button>
        </div>
      </el-dialog>
      <el-dialog title="配载单上传"  :visible.sync="dialogVisible" width="580" :before-close="dialogBeforeClose">
        <div style="max-height: 50vh;overflow: scroll;">
          <upload-excel-component :on-success="handleSuccess" :before-upload="beforeUpload" />
          <el-table :data="uploadTableData" border highlight-current-row style="width: 100%;margin-top:20px;" >
          <el-table-column v-for="item of tableHeader" :key="item" :prop="item" :label="item" />
        </el-table>
        </div>
        <div slot="footer">
            <el-button :loading="uploadLoading" @click="dialogVisible = false">取 消</el-button>
            <el-button :loading="uploadLoading" type="primary" @click="saveInvoiceList()">确 定</el-button>
        </div>
    </el-dialog>
    <el-dialog
    :visible.sync="dialogTarnShow"
    title="查看凭证"
    width="65%"
    :before-close="()=>dialogTarnShow = false"
  >
    <resultTrans v-for="item in showTableTrans" :key="item.id" :tableData="item" :classList="kjClassList"></resultTrans>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTarnShow = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
    <el-dialog
    :visible.sync="dialogYacc"
    title="生成凭证"
    width="80%"
    :before-close="()=>dialogYacc = false"
  >
    <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
      v-model="monthDateAcc"
      type="month"
      value-format="yyyyMM"
      placeholder="选择日期">
    </el-date-picker>
    </span>
    <Subacc :tableData="tableDataAcc"  :accounts="ledgerAccountList" @update:tableData="v=> tableDataAcc = v"  @ledgerAccount="v=> ledgerAccount = v"  :classList="kjClassList"></Subacc>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogYacc = false">取消</el-button>
        <el-button type="primary" @click="saveYacc()">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
    </div>
  </section>
</template>



<script>
import 'vxe-table/lib/style.css'
import {
  addStowageByJsonArray,
    getOnAccountList,
    getShipLineSummary,
    taskGetNew,
  updateOnAccountProcess
  } from "@/api/system/onAccount.js";
  import dayjs from 'dayjs'
import VXETable from 'vxe-table'
  import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
  import 'vxe-table/lib/style.css'
  import {getAcctgTransListByCode, getHaokjSub,getIsCodeByPidAndCode,getStowageVocherListByCode,saveVoucher as saveVoucherProcessapi} from '@/api/business/processapi'
import { getWuLiuSpPerspnListByProcessId } from '@/api/system/process'
import UploadExcelComponent from '@/components/UploadExcel/index.vue'
  import {fmtDictionary} from "@/utils/util"
   import {
    calcMergeOfColumns,calcMergeOfColumns2,getPrevMonthDay
  } from "@/utils/utils";
import {getDictionaryList} from "@/api/system/baseInit";
import { saveVoucher } from "@/api/system/processVoucherRecord";
  VXETable.use(VXETablePluginExportXLSX)
const OperationLogApi = require('@/api/system/operationLog.js')
import AccModel from '@/components/AccModel/index.vue'
  import {PZD_EXCEL_URL} from "@/utils/config";
  import resultTrans from '@/views/system/businessPayment/resultTrans.vue';
  import Subacc from '@/views/system/businessPayment/subacc.vue'
  import currency from 'currency.js'
    export default {
  name: "shipIndex",
  components: { UploadExcelComponent,Subacc,resultTrans,AccModel },
  data() {
    return {
      tdata:{},
      codeKey: 'receivableShipVouchers_pzd',
      // codeKeyYF: 'receivableVouchers_pzdyf',
      codeListKey: 'rKey',
      codeListCount: 'rCount',
      // codeListKeyYF: 'rKeyYF',
      // codeListCountYF:'rCountYF',
            monthDateAcc: dayjs().format('YYYYMM'),
            kjClassList: [],
            tableDataAcc: [],
            dialogYacc: false,
            showTableTrans: [],
            dialogTarnShow: false,
            pzdExcelUrl:PZD_EXCEL_URL,
            uploadLoading:false,
            tableHeader:["航次号","船名","mmsi","收支类型","客户全称","客户简称","货物类型","总价","税率","吨位","费用类型","装货港","装货码头","卸货港","卸货码头"],
            dialogVisible: false,
            uploadTableData: [],
            areaName: '',
            ship10PriceList:[],
              goodsTypeNames:'',
              loadingHeZhi:false,
              mergeCells:[],
              mergeShipPayCells:[],
              isMergeYunFei:false,
              toolbarButtons: [],
              isShowPrintHistory: false,
            printHistoryData: [],
            typeAccountSelect:'',
            typeAccountList: [{
              label: '全部',
              value: ''
            },{
              label: '成功',
              value: 'NoGBSD'
            },
            {
              label: '过驳',
              value: 'GB'
              },
              {
              label: '山东',
              value: 'SD'
            },
            ],
              timeline: {
                status: null,
                start: {
                  content: '发起申请',
                  item: {
                    id: 0,
                    name: '',
                    avatar: ''
                  }
                },
                sp: [],
                ccs: {
                  content: '抄送(0人)',
                  items: []
                }
              },
              showProcess:false,
              //----------------------------------------------------------------
              tableData:[],
              pageSize: 10,
              applyStatus:2,
              applyStatusList:[
                {
                  code:1,
                  value:"挂账待办"
                },
                {
                  code:2,
                  value:"挂账已办"
                },
            ],
            summaryFiledList:['settleTong','priceNo','price','priceSum','modelPriceSum'],
              pageNo:1,
              total:0,
              zonglist:[],
              shippaylist:[],
              yufeilist:[],
              huowulist:[],
              shipcostlistshou:[],
              shipcostlistzhi:[],
              shipcostlist:[],
              Dianinterest:0,
              Maifinancial:0,
              taxIncomcaiwu:0,
              taxIncomcaiwushuino:0,
            name: null,
            endPortName: null,
            unloadWharfName:null,
              startPortName:null,
              loadWhrafName: null,
              shipLine:{},
              showheji:false,
              yunfei:0,
              yun:0,
              dictionaryLists:[],
              supplierList:[],
              customerList:[],
              queryParams:{
                shipTime:null,
                shipName:null,
                onAccountNum:null,
                shipLiGangTime: null,
                voyageNumber:null,
                creditTime:getPrevMonthDay(),
            },
            spLogList: [],
              deptCode:'',
            showTimeTittle:'离港',
            ledgerAccount:'',
            ledgerAccountList:[],
            isCertModel:'1',
            };
  },
  created() {
    this.loadKeMuList()
  },
  mounted() {
    var getSupplier
    var getCustomer
    // if (this.$route.name === 'onAccountHS') {
    //   this.deptCode = 'HNHS'
    //   this.showTimeTittle = '卸空完货'
    //   getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuexHs')
    //   getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuexHs')
    // } else if (this.$route.name === 'onAccountShip') {
      this.deptCode = 'SHIPMANAGE'
      this.showTimeTittle = '卸空完货'
      // getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuexHs')
      // getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuexHs')
    // } else {
    //   this.deptCode = ''
    //   this.showTimeTittle = '离港'
    //   getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
    //   getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuex')
    // }

        var dic = getDictionaryList("cost_type,items_occurred,bill_tax,contract_company",this.deptCode)
        Promise.all([getSupplier,getCustomer,dic]).then(values => {
          let res  = values[0];
          if(res){
            this.supplierList = res.supplierList
          }
          res  = values[1];
          if(res){
            this.customerList = res.customerList
          }
          res  = values[2];
          if (res) {
            this.dictionaryLists = res.map
          }
          this.topage()
        })
  },
  filters: {
    strSubFil(s) {
      if (!s) {
        return ''
      }
      return s.substring(0, 1)
    }
  },
  computed: {
    monthDateStr() {
      return dayjs(this.monthDateAcc).format('YYYY年MM月')
    },
    totalDebit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.debit||0)), 0);
    },
    totalCredit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.credit||0)), 0);
    },
  },
  methods: {
    updateAccModel(type){
      this.isCertModel = type
      this.uplistByListPz()
    },
    saveAccTable(type = '1', func = undefined) {
      // 验证数据，
      if (this.totalCredit != this.totalDebit) {
        this.$message.error('借方金额与贷方金额不一致')
        return
      }
      // if (type == '-1' && this.totalCredit > this.paymentTotal) {
      //   this.$message.error('借贷金额大于付款金额')
      //   return
      // }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })

      saveVoucherProcessapi(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,this.sarAccSingle?'1':'0',this.ledgerAccount).then((data) => {
        if (data.result) {
          this.$message.success('保存成功')
          func && func()
        } else {
          this.$message.error(data.msg || '保存失败')
        }
        this.loadIsCodeByPidAndCode(this.tableProcessIdAcc, type).then((res) => {
            // if (type == this.codeKeyYF) {
              // this.upPzCodeToList(res,this.codeListKeyYF,this.codeListCountYF)
            // } else {
              this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
            // }
        })
        loading.close()
      }).catch((err) => {
        this.$message.error('保存失败')
        loading.close()
      })
    },
    saveYacc() {
      this.saveAccTable(this.ctypeCode, () => {
        this.dialogYacc = false
      })
    },
    showYacc(data,type) {
      this.ctypeCode = type
      this.loadAccTable(data.id, this.ctypeCode, () => {
        this.dialogYacc = true
        // 刷新
      })
    },
    loadAccTable(pid, type='1',func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = pid
      this.voucherCode = ''
      // if (type == '-1' && !this.paymentTotal) {
      //   loading.close()
      //   return
      // }
      // getStowageVocherListByCode(pid, type,this.sarAccSingle?'1':'',this.paymentTotal).then((res) => {
      getStowageVocherListByCode(pid, type).then((res) => {
        // console.log('res', res)
        // let list = []
        // list = res && res?.data ? res.data : []
        this.tableDataAccCode(res)
        func && func()
        loading.close()
      }).catch((err) => {
        this.$message.error(err.message||'暂不支持此类型')
        loading.close()
      })
    },
    tableDataAccCode(res) {
        const list = res && res.data ? res.data : []
        this.voucherCode = res && res.code
        this.tableDataAcc = list || []
        this.ledgerAccountList = res && res.accounts
        this.upSubIdByFind()
    },
    showTranByCode(data,code) {
      this.loadTranListByProcessIdAndCode(data.id, code, () => {
        this.dialogTarnShow = true
      })
    },
    loadTranListByProcessIdAndCode(pid, code,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransListByCode(pid, code).then(res => {
        this.showTableTrans = res.result
        func && func()
      }).finally(() => {
        loading.close()
      })
    },
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
          this.upSubIdByFind()
        }
      })
    },
    upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        // tableDataAcc 中 findSub 获取 kjClassList glAccountName 相等 ，treePath(2241^********^) = subject (2241)^
        //tableDataAcc -> findSub,subject
        // kjClassList -> glAccountName,treePath, glAccountCode
         // 承兑 最后一个科目改成1121
        //  if (3 == this.paymentType) {
        //   this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1121'
        //   this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1121'
        //  }
        //   if (1 == this.paymentType) {
        //     this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1001'
        //     this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1001'
        //   }

        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^')  && item.glAccountName == this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }
      }
    },
    downloadTemplate(url) {
      window.open(url)
    },
    saveInvoiceList() {
      this.uploadLoading=true
      // 保存
      addStowageByJsonArray(this.uploadTableData).then(res => {
        this.topage()
      }).finally(() => {
        this.uploadLoading = false
        this.dialogVisible = false
      })
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 5
      if (isLt1M) {
        return true
      }

      this.$message({
        message: '请不要上传大于5m的文件.',
        type: 'warning'
      })
      return false
    },
    handleSuccess({ results, header }) {
      this.uploadTableData = results
      // this.tableHeader = header
    },
    dialogBeforeClose() {
      this.dialogVisible=false
    },
    openUploadInvoce(type = 1) {
      this.dialogVisible = true
      this.uploadTableData = []
      // this.tableHeader = []

    },
    loadSpListByProcessId(processId) {
      this.spLogList = []
      getWuLiuSpPerspnListByProcessId(processId,this.deptCode).then(res => {
        console.log('process',res)
        if (res.data) {
          this.spLogList = res.data
        }
      })
    },
    footerMethodShipCost({columns, data}) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (columnIndex === 3 || columnIndex === 4) {
            return this.sumNum2(data, column.property)
          } else {
            return ''
          }
        })]
    },
    footerMethod({ columns, data }) {
      console.log('fm', columns, data)
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (!this.summaryFiledList.includes(column.property)){
            return ''
          }
          if (column.property === 'settleTong') {
            // 吨位单独处理
            // if (this.summaryFiledList.includes(column.property)) {
              // 吨位只计算海运费
              const d = data.filter(item => item.costtype === '海运费' && (!item.lighter || 1 != item.lighter)  )
              return this.sumNum3(d, column.property)
            // }
            // return null
          } else {
            // 小计单独处理
            if (column.property === 'priceSum') {
              // 同一id的小计只计算一次
              // let customerId
              let count = 0
              data.forEach(item => {
                // if (item.customerid == customerId && item.rowSpan > 1) {

                // } else {
                //   customerId = item.customerid
                  count = currency(count).add(currency(Math.abs(Number(item[column.property])))).value
                // }
                // if (!idList.includes(item.id)) {
                //   idList.push(item.id)
                //   count = currency(count).add(currency(Math.abs(Number(item[column.property])))).value
                // }
              })
              // return parseFloat(Math.abs(count).toFixed(2))
              return currency(count,{ symbol: '', precision: 2 }).format()
            }
            // if (this.summaryFiledList.includes(column.property)) {
              return this.sumNum2(data, column.property)
            // }
            // return null
          }
        })
      ]
    },
    sumNum3 (list, field) {
      let count = 0
      list.forEach(item => {
        // count += Number(item[field])
        count += round(Number(item[field]), 3)
        // count = currency(count).add(currency(Math.abs(Number(item[field])))).value
      })
      return Number.parseFloat(Math.abs(count).toFixed(3))
    },
    sumNum2 (list, field) {
      let count = 0
      list.forEach(item => {
        // count += round(Number(item[field]), 2)
        count = currency(count).add(currency(Math.abs(Number(item[field])))).value
      })
      // return parseFloat(Math.abs(count).toFixed(2))
      return currency(count,{ symbol: '', precision: 2 }).format()
    },

    rowClassNameYunfei({ row, rowIndex }) {
      // 金额为0 增加隐藏样式
      // eslint-disable-next-line eqeqeq
      if (row.price == 0) {
        return 'hide-row'
      }
      return ''
    },
    mergeYufei() {
          this.isMergeYunFei = !this.isMergeYunFei
          // 合并或取消合并
          if (this.isMergeYunFei) {
            // 合并 保存原数据
            this.tmpYunfeiList = this.yufeilist.concat()
            // 新数据
            const nlist = []
            this.yufeilist.forEach(item => {
              const fitem = nlist.find(im => im.customerid == item.customerid)
              if (fitem) {
                fitem.price = currency(fitem.price).add(currency(item.price)).value
                fitem.priceNo = currency(fitem.priceNo).add(currency(item.priceNo)).value
              } else {
                nlist.push({
                  customerid: item.customerid,
                  price: item.price || 0,
                  priceNo: item.priceNo || 0
                })
              }
            })
            this.yufeilist = nlist
          } else {
            // 恢复原数据
            this.yufeilist = this.tmpYunfeiList
          }
        },
        /**
         * 检查之前是否有过打印记录
         */
    verifyPrintBefore() {
          const loading = this.$loading({
            lock: true,
            text: '加载中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          OperationLogApi.query({
            // type = 1：查看合值
            type: 1,
            params1: this.shipLine.id
          }).then(res => {
            if (res.content.length < 1) {
              // 首次打印
              this.onCloseValuePrint()
              return
            }
            // 以前打印过，展示操作历史
            this.isShowPrintHistory = true
            this.printHistoryData = res.content
          }).catch(err => {
            // nothing.
          }).finally(_ => {
            loading.close()
          })
    },
    printContentByRef(refName) {
      return this.$refs[refName] && this.$refs[refName].exportData({
        original: false,
        type: 'html',
        download: false,
        remote: false,
        print: true,
        isMerge: true
      })
    },
        /**
         * 打印合值
         */
    onCloseValuePrint() {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // var divEl = document.getElementById('total-cost').outerHTML
      // 增加左侧3cm空白 装订用
      // q: 1cm = ?px
      // a: 1cm = 37.795275590551px
      // 3cm = 113.38582677165px
      let divEl = `<div style="margin-left:180px;font-size:22px;">`
       divEl += document.getElementById('hangci-biaoti-id').outerHTML
      // xTableYunfei 运费收入

      //  this.$refs.xTableYunfei.exportData({
      //   original: false,
      //   type: 'html',
      //   download: false,
      //   remote: false,
      //   print: true
      // }).then(res => {
      //   console.log('c',res.content)
      // })
      // xTableShipCost 船上费用收入
      // xTableShipPay 海运费成本
      // xTableHuoWu 中转费成本
      // xTableShipCostOut 船上费用支出
      Promise.all([this.printContentByRef('xTableYunfei'),
        this.printContentByRef('xTableShipCost'),
        this.printContentByRef('xTableShipPay'),
        this.printContentByRef('xTableHuoWu'),
        this.printContentByRef('xTableShipCostOut'),this.printContentByRef('xTableShipPay10Tab')]).then(res => {
          divEl+= `<div style="margin-right:60px;">`
          if (res[0]) {
            divEl += `<div style="font-weight: bold;margin:10px 0 5px 0;">收入</div>`
            divEl += res[0].content
          }
          if (res[1]) {
            divEl += `<div style="font-weight: bold;margin:10px 0 5px 0;">船上费用收入</div>`
            divEl += res[1].content
          }
          if (res[2]) {
            divEl += `<div style="font-weight: bold;margin:10px 0 5px 0;">成本</div>`
            divEl += res[2].content
          }
          if (res[3]) {
            divEl += `<div style="font-weight: bold;margin:10px 0 5px 0;">中转费成本</div>`
            divEl += res[3].content
          }
          if (res[4]) {
            divEl += `<div style="font-weight: bold;margin:10px 0 5px 0;">船上费用支出</div>`
            divEl += res[4].content
          }
          if (res[5]) {
            divEl += `<div style="font-weight: bold;margin:10px 0 5px 0;">船方承担</div>`
            divEl += res[5].content
          }
        }).finally(() => {
          divEl += document.getElementById('sp-list-id').outerHTML
          // 每个表格内容单独调用 处理分页表头情况
          divEl += `</div></div><style>

                            .vxe-table--empty-placeholder{
                                display: none;
                              }

                              .vxe-table--empty-block{
                                display: none;
                              }
                              .vxe-loading--chunk{
                                display:none;
                              }

                              .vxe-cell{
                                text-align: center;
                              }
                              .el-divider{
                                margin-top:10px;
                              }
                              #total-cost-print{
                                position:absolute;
                                transform-origin:left top;
                                // transform: scale(0.8);
                              }
                              #mergeBtn{
                                display:none;
                              }
                              // 表格 最后两列靠右对齐
                              .txtright{
                                text-align:right
                              }
                              // vxe-table 有两个table 组成一个表格，一个是表头，一个是表体，如何让表头分页打印时每页都显示





                              // thead{
                              //   display:table-header-group;
                              // }
                              // tfoot{
                              //   display:table-footer-group;
                              // }
                          </style>`
                this.$XPrint({
                  sheetName: '打印合值'+' 挂账号：'+this.publicFmtn(this.shipLine.onAccountNum),
                  content: divEl,
                  beforePrintMethod: ({ content }) => {
                    // 拦截打印之前，记录操作日志,应该监听打印后的操作，但是没有找到合适的方法
                    OperationLogApi.add({
                      type: 1,
                      params1: this.shipLine.id
                    }).then(res => {
                      // save success
                    }).catch(err => {
                      // nothing...
                    }).finally(_ => {
                      this.isShowPrintHistory = false
                    })
                    return content
                  }
                })
                loading.close()
        })

    },
    applyTypeQuery(item) {
      this.typeAccountSelect = item.value
      this.topage()
    },
        applyStatusQuery(item){
          const id = item.code
          this.applyStatus = id
          this.topage()
        },
        showProcessDetail(id){
          taskGetNew(id,this.deptCode).then(res => {
            this.timeline.status = res.data.statusId
            this.timeline.start = res.data.spFlow.start
            this.timeline.sp = res.data.spFlow.sp
            this.statusId = res.data.nodeId;
            this.timeline.ccs = res.data.spFlow.ccs
            for (var i = 0; i < this.timeline.sp.length; i++) {
              if (this.timeline.sp[i].content.indexOf("审批中") != -1) {
                this.currentSpId = this.timeline.sp[i].item.id
              }
            }
            this.showProcess = true
          })
        },
        handleClosesshowProcess(){
          this.showProcess = false
        },
        toAgree(shiplineId,processId,status){
          this.$prompt('请输入凭证号', '提示', {
            confirmButtonText: '提交',
            cancelButtonText: '取消',
          }).then(({ value }) => {
            const loading = this.$loading({
              lock: true,
              text: '数据提交中，请稍后！',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            var save = saveVoucher(value,processId)
            var update = updateOnAccountProcess(processId, status,null,this.deptCode)
              Promise.all([save,update]).then(values => {
                const res1  = values[0];
                const res2 = values[1];
                if(res1 && res2){
                  this.$message({
                    type: 'success',
                    message: '操作成功！'
                  });
                  this.handleCloseshowheji()
                  loading.close()
                }
              })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
        },
        toReject(processId,status){
          this.$prompt('请输入退回原因', '提示', {
            confirmButtonText: '提交',
            cancelButtonText: '取消',
          }).then(({ value }) => {
            const loading = this.$loading({
              lock: true,
              text: '数据提交中，请稍后！',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            updateOnAccountProcess(processId, status,value,this.deptCode).then(res => {
              if (res) {
                this.$message({
                  type: 'success',
                  message: '退回成功！'
                });
                this.handleCloseshowheji()
                loading.close()
              }
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
        },
        onAccountFinish(shiplineId,processId,status){
          if(status == 0){
            this.toReject(processId,status)
          } else {
            this.toAgree(shiplineId,processId,status)
          }
        },
        getyunfei(yunlist){
          this.zonglist=[]
          var yun = 0
          var yunshui = 0
          var dai = 0
          var daishui = 0
          yunlist.forEach(resz =>{
            if (resz.costtype === '海运费'){
              // yun += resz.price
              yun = currency(yun).add(resz.price).value
              // yunshui += resz.priceNo
              yunshui = currency(yunshui).add(resz.priceNo).value
            } else if(resz.costtype === '代理费'){
              // dai += resz.price
              dai = currency(dai).add(resz.price).value
              // daishui += resz.priceNo
              daishui = currency(daishui).add(resz.priceNo).value
            }
          })
          var zong = {
            name:"海运费收入",
            price:this.numbern(yun),
            priceno:this.numbern(yunshui)
          }
          this.zonglist.push(zong)
          var daili = {
            name:"代理费收入",
            price:this.numbern(dai),
            priceno:this.numbern(daishui)
          }
          this.zonglist.push(daili)
          var zongchuan = {
            name:"海运费成本",
            price:this.numbern(this.yunfei),
            priceno:this.numbern(this.yun)
          }
          this.zonglist.push(zongchuan)
          console.info(this.zonglist)
        },
    mergeSumYunfei(list) {
          if(!list){
            return
          }
          const nlist = []
           list.forEach(item => {
            // 计算小计
              const fitem = nlist.find(im => im.customerid == item.customerid)
              if (fitem) {
                fitem.price = currency(fitem.price).add(currency(item.price)).value
                fitem.priceNo = currency(fitem.priceNo).add(currency(item.priceNo)).value
                fitem.rowSpan ++
              } else {
                nlist.push({
                  customerid: item.customerid,
                  price: item.price || 0,
                  priceNo: item.priceNo || 0,
                  rowSpan:1
                })
              }
           })
        list.forEach(item => {
              const fitem = nlist.find(im => im.customerid == item.customerid)
              if (fitem) {
                item.priceSum = fitem.price
                item.rowSpan = fitem.rowSpan
              }
          })

    },
    initHeJiData() {
          this.shippaylist = []
          this.yufeilist = []
          this.huowulist = []
      this.mergeCells = []
          this.mergeShipPayCells = []
          this.Dianinterest = undefined
          this.Maifinancial = undefined
          this.taxIncomcaiwushuino = undefined
          this.taxIncomcaiwu = undefined
      this.startPortName = undefined
      this.endPortName = undefined
      this.unloadWharfName = undefined
          this.loadWhrafName = undefined
          this.yunfei = undefined
          this.yun = undefined
          this.name = undefined
          this.shipcostlistzhi = []
          this.shipcostlistshou = []
      this.areaName = ''
          this.goodsTypeNames=''
        },
    openhe(obj) {
      console.log('obj',obj)
          this.loadingHeZhi = true
          this.showheji = true
          this.isMergeYunFei=false
      this.shipLine = obj

          this.initHeJiData()
          // this.shippaylist = []
          // this.yufeilist = []
          // this.huowulist = []
          // this.mergeCells = []
          var querydata = {
            shiplineid: obj.id,
            deptCode: this.deptCode,
          }
          // 获取审批人
          this.loadSpListByProcessId(obj.processId)
          getShipLineSummary(querydata).then(res=>{
            if (res!=undefined) {
              this.tdata = res.data

              this.Dianinterest = res.Dianinterest
              this.Maifinancial = res.Maifinancial
              this.taxIncomcaiwushuino = res.taxIncomcaiwushuino
              this.taxIncomcaiwu = res.taxIncomcaiwu
              // this.startPortName = res.startPortName
              // this.startPortName = this.tdata?.content?.loadingPortName
              this.startPortName = this.tdata.content.loadingPortName
              this.loadWhrafName = res.loadWhrafName
              // this.endPortName = res.endPortName
              this.endPortName = this.tdata.content.unloadingPortName
              this.unloadWharfName = res.unloadWharfName
              this.areaName = res.areaName
              // this.goodsTypeNames = res.goodsTypeNames
              this.goodsTypeNames = this.tdata.content.goodsName
              this.ship10PriceList = res.ship10PriceList
              // 船价合计，运价*结算吨位
              this.yunfei = res.yunfei
              this.yun = res.yunfeishui
              this.name = res.name
              this.shippaylist = res.yunFeiList || []
              // biome-ignore lint/complexity/useOptionalChain: <explanation>
              if (this.shippaylist && this.shippaylist.length) {
                this.mergeShipPayCells = calcMergeOfColumns2(this.$refs.xTableShipPay, this.shippaylist, ['customerid'], ['customerid', 'priceSum']);
                this.mergeShipPayCells = this.mergeShipPayCells.concat(calcMergeOfColumns2(this.$refs.xTableShipPay, this.shippaylist, ['modelName'], ['modelName', 'modelPriceSum']))
              }
              // res.yunFeiList.forEach(res =>{
              //   // if (res.tax){
              //     this.shippaylist.push(res)
              //   // }
              // });
              // 运费 吨位 单价
//               客户名称
// officeName
// 费用类型
// costtype
// 税率
// tax
// 吨位
// settleTong
// 单价
// settlePrice
// 金额
// price
// 小计
// priceSum
              const _content = this.tdata.content
              const _vcostList = this.tdata.voyageCostGroupList || []
              const _freight = ((_content.price || 0) * (_content.tonnage || 0)) + (_content.settleSumPrice || 0)
              let _sum = 0
              for(let i=0;i<_vcostList.length;i++){
                const item = _vcostList[i]
                for(let j=0;j<item.costDtoList.length;j++){
                  const s = item.costDtoList[j]
                  _sum+=s.prices
                }
              }
              const _ylist = []
              _ylist.push({
                officeName:'成功网联',
                costtype:'海运费',
                tax:_content.priceTaxRate ? `${(_content.priceTaxRate * 100).toFixed(0)}%`:'无税',
                settleTong:_content.tonnage,
                settlePrice:_content.price,
                price:_freight,
                priceSum:_freight+_sum
              })
              // 滞期费
              // voyageCostGroupList

              for(let i=0;i<_vcostList.length;i++){
                const item = _vcostList[i]
                for(let j=0;j<item.costDtoList.length;j++){
                  const s = item.costDtoList[j]
                  _ylist.push({
                    officeName:'成功网联',
                    costtype:s.typeName,
                    tax:s.priceTaxRate ? `${(s.priceTaxRate * 100).toFixed(0)}%`:'无税',
                    settleTong:s.num,
                    settlePrice:s.unitPrice,
                    price:s.prices,
                    priceSum:0
                  })
                }
              }
              // this.yufeilist = res.sumc
              this.yufeilist = _ylist
              // res.sumc.forEach(ress =>{
              //   if (ress.tax){
              //     this.yufeilist.push(ress)
              //   }
              // })
              // 运费增加合计
              // this.mergeSumYunfei(this.yufeilist)
              this.mergeCells = calcMergeOfColumns2(this.$refs.xTableYunfei, this.yufeilist, ['customerid'],['customerid','priceSum']);
              // this.getyunfei(res.sumc);
              // this.huowulist = res.costlist;
              // if (res.costlist) {
              //   res.costlist.forEach(citem => {
              //     if (citem && citem.list) {
              //       this.huowulist = this.huowulist.concat(citem.list)
              //     }
              //   })
              // }

              // for(var i = 0;i<res.shipcostlist.length;i++){
              //   var ress = res.shipcostlist[i]
              //   if (ress.isIncome === 0){
              //     this.shipcostlistzhi.push(ress)
              //   } else {
              //     // this.shipcostlistshou.push(ress)
              //   }
              // }
            }
          }).finally(() => {
            this.loadingHeZhi = false
          })

        },
        handleCloseshowheji(){
          this.showheji = false
          this.shipcostlistzhi = []
          this.shipcostlistshou = []
          this.topage(this.pageNo)
        },
        topage(e){
          if(e){
            this.pageNo = e
          }
          var param = {
            pageNo:this.pageNo,
            pageSize:this.pageSize,
            applyStatus:this.applyStatus,
            shipName:this.queryParams.shipName,
            onAccountNum:this.queryParams.onAccountNum,
            shipLiGangTime:this.queryParams.shipLiGangTime,
            shipTime: this.queryParams.shipTime,
            voyageNumber: this.queryParams.voyageNumber,
            typeAccount: this.typeAccountSelect,
            deptCode: this.deptCode,
            guaTime: this.queryParams.creditTime,
          }
          getOnAccountList(param).then(res=>{
            if (res!=undefined) {
              this.tableData = res.list
              this.total = res.total
              this.uplistByListPz()
            }
          })
    },
    uplistByListPz(){
      // pids this.dataList
      const pids = this.tableData && this.tableData.map(item => item.id).join(',')
      if (!pids) {
        return
      }
      // 异步加载数据并更新
      // , this.loadIsCodeByPidAndCode(pids, this.codeKeyYF)
      Promise.all([this.loadIsCodeByPidAndCode(pids, this.codeKey)]).then(([res1]) => {
        this.upPzCodeToList(res1, this.codeListKey, this.codeListCount)
        // this.upPzCodeToList(res2,this.codeListKeyYF,this.codeListCountYF)
    })
      // this.loadIsCodeByPidAndCode(pids, this.codeKey, (res) => {
      //     this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
      // })
      // this.loadIsCodeByPidAndCode(pids, this.codeKeyYF, (res) => {
      //     this.upPzCodeToList(res,this.codeListKeyYF,this.codeListCountYF)
      // })
    },
    upPzCodeToList(res,key,countKey) {
      if (!res) {
          return
      }
      // for (let i = 0; i < this.tableData.length; i++) {
      //     const item = this.tableData[i]
      //     if (res[item.id]) {
      //         item[key] = res[item.id].code
      //         item[countKey] = res[item.id].count
      //     }
      // }
      // this.tableData = this.tableData.slice()
      this.tableData = this.tableData.map(item => {
      if (res[item.id]) {
          return {
            ...item,
            [key]: res[item.id].code,
            [countKey]: res[item.id].count,
          };
        }
        return item;
      });
    },
    loadIsCodeByPidAndCode(pids,code) {
     return getIsCodeByPidAndCode(pids,code)
    },
        eventPage(e){
          this.topage(e);
        },
        numbern(cellValue){
          var v = 0;
          if (cellValue != undefined && cellValue != null && cellValue != "") {
            v = cellValue.toFixed(2);

          }
        // return  currency(v,{ symbol: '', precision: 2 }).format()
          return v;
        },
        priceFormat(cellValue) {
           var v = 0;
          if (cellValue != undefined && cellValue != null && cellValue != "") {
            v = cellValue.toFixed(2);

          }
          return  currency(v,{ symbol: '', precision: 2 }).format()
          // return v;
        },
        priceFormatFmt({ row, column, cellValue, index }) {
          // console.log('cvale',cellValue)
           var v = 0;
          if (cellValue != undefined && cellValue != null && cellValue != "") {
            v = cellValue
          }
          return  currency(v,{ symbol: '', precision: 2 }).format()
        },
        publicFmtn(cellValue) {
          var v = "--";
          if (cellValue != undefined && cellValue != null && cellValue != "") {
            v = cellValue
          }
          return v;
        },
          publicFmt(row, column, cellValue, index) {
            var v = "--";
            if (cellValue != undefined && cellValue != null && cellValue != "") {
              v = cellValue
            }
            return v;
          },
        publicFmtVxe({row, column, cellValue, index}) {
          var v = "--";
          if (cellValue != undefined && cellValue != null && cellValue != "") {
            v = cellValue
          }
          return v;
        },
        publicFmtnumber({row, column, cellValue, index}) {
          var v = 0;
          if (cellValue != undefined && cellValue != null && cellValue != "") {
            v = cellValue.toFixed(2);

          }
          return v;
        },
          myFmtDateTime(cellValue,fmtstr){
            if(cellValue==undefined||cellValue==null||cellValue==''){
              return ' '
            }
            return dayjs(cellValue).format(fmtstr)
          },
        bill_taxFmtt({row, column, cellValue, index}) {
          return fmtDictionary(cellValue, this.dictionaryLists["bill_tax"]);
        },
        cost_typeFmt({row, column, cellValue, index}) {
          return fmtDictionary(cellValue, this.dictionaryLists["cost_type"]);
        },
        items_occurredFmty({row, column, cellValue, index}){
          return fmtDictionary(cellValue, this.dictionaryLists["items_occurred"]);
        },
        companyFmtn(cellValue) {
          return fmtDictionary(cellValue,this.dictionaryLists['contract_company']);
        },
        fmtsuName({ row, column, cellValue, index }) {
          return this.fmtsuNamen(cellValue)
          // var v = "--";
          // for (var i = 0; i < this.supplierList.length; i++) {
          //   var tmp = this.supplierList[i];
          //   if (cellValue == tmp.id) {
          //     v = tmp.simpleName;
          //     break;
          //   }
          // }
          // return v;
        },
        fmtsuNamen(cellValue) {
          var v = "--";
          for (var i = 0; i < this.supplierList.length; i++) {
            var tmp = this.supplierList[i];
            if (cellValue == tmp.id) {
              v = tmp.simpleName;
              break;
            }
          }
          return v;
        },
        kehuListFmtn(cellValue){
          var v = '--'
          for(var i=0;i<this.customerList.length;i++){
            var tmp = this.customerList[i]
            if(cellValue == tmp.id){
              v =  tmp.abbreviationName
              break;
            }
          }
          return v
        },
      }
    }
</script>

<style scoped>
  .el-table-info >>> .cell {
    text-align: center;
  }

  .el-table-info >>> th {
    background: #f5f7fa;
  }

  .hangci-biaoti {
    text-align: left;
    margin: 6px 30px 6px 0;
  }

  .hangci-biaoti11 {
  }

  .hangci-biaoti12 {
    margin-left: 30px;
  }
  .el-table-info >>> .vxe-cell{
    text-align: center;
  }

  .process-drawer{
    text-align: left;
  }
  #drawerBody{
    padding: 10px;
  }
  #timelineBody > * {
    text-align: left !important;
  }
  .wh40{
    width: 40px;
    height: 42px;
    padding: 0 !important;
    margin: 0 !important;
  }
  .timelineContent {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    align-content: space-around;
  }
  .diy-avatar{
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #C0C4CC;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 5px;
  }
  #total-cost {
    height: calc(100vh - 176px);
    overflow-y: scroll;
  }

  .btnCol{
    color: #13ce66;
  }
  .btnCol:hover{
    color:#10b85a;
  }
</style>
<style>
@media print {
      #total-cost {
      height: unset;
      overflow-y: unset;
    }
    .hide-row{
      display: none;
    }

  }
</style>

