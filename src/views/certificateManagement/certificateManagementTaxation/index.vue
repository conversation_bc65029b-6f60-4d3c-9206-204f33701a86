<template>
  <section>
    <HaokjBtn></HaokjBtn>
    <el-tabs v-model="activeName" @tab-click="handleClick"  type="border-card">
      <el-tab-pane label="进项" name="first"><income></income></el-tab-pane>
      <el-tab-pane label="销项" name="second"><outcome-tab></outcome-tab></el-tab-pane>
    </el-tabs>
  </section>
</template>
<script>
import income from './income.vue';
import outcomeTab from './outcomeTab.vue';
import HaokjBtn from '@/components/AccModel/haokjBtn.vue'

export default{
  name: 'CertificateManagementTaxation',
  components: { income,outcomeTab,HaokjBtn },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
