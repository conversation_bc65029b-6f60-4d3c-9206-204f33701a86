<template>
  <section>
    <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
      v-model="monthDateAcc"
      type="month"
      value-format="yyyyMM"
      placeholder="选择日期">
    </el-date-picker>
    </span>
    <Subacc :tableData.sync="tableDataAcc"   :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v"  ></Subacc>
    <div class="dialog-footer" >
      <el-button type="primary" @click="saveYacc()">
        提交
      </el-button>
    </div>
</section>
  </template>
  <script>
  import { saveVoucher} from '@/api/business/processapi'
  import Subacc from '@/views/system/businessPayment/subacc.vue'
  import dayjs from 'dayjs'
  import {getTokenCompanyList} from '@/api/system/baseInit'
  export default {
    name: 'NewCertificateSave',
    components: {Subacc},
    data() {
      return {
        monthDateAcc: dayjs().format('YYYYMM'),
        tableDataAcc: [],
        ledgerAccount: '',
        ledgerAccountList: [],
        code: 'newCert',
      }
    },
    created() {
      // this.loadKeMuList()
      this.loadData()
    },
    methods: {
      loadData() {
        this.ledgerAccountList=[]
        getTokenCompanyList().then(res => {
          console.log('token', res)
          this.ledgerAccountList = res.list.map(item => {
            return {
              ...item,
              code: item.appCode,
              name: item.appName+':'+item.companyName
            }
          })
          this.initCert()
        })
      },
      generateTimeBasedRandom() {
        // 1. 获取当前时间戳（毫秒）
        const timestamp = Date.now();

        // 2. 映射到4-8位范围（1000-********）
        const minRange = 1000;
        const maxRange = ********;
        // 时间戳取模并映射到目标范围
        const baseValue = timestamp % (maxRange - minRange + 1) + minRange;

        // 3. 加入1-999的随机扰动（避免连续时间生成相同值）
        const randomOffset = Math.floor(Math.random() * 999) + 1;
        return baseValue + randomOffset;
      },
      initCert() {
        this.tableDataAcc = [{debit:0,
          credit:0},{debit:0,
            credit:0},{debit:0,
              credit:0},{debit:0,
                credit:0},{debit:0,
                  credit:0}] // 科目列表
      },
      saveYacc() {
        this.saveAccTable(this.code, () => {
          this.initCert()
        })
      },
      saveAccTable(type, func=undefined) {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.2)'
        })
        this.tableProcessIdAcc = 'newCert'+dayjs().format('YYYYMMDD')+this.generateTimeBasedRandom()
        saveVoucher(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,'',this.ledgerAccount).then((data) => {
          if (data.result ) {
            this.$message.success('保存成功')
            func && func()
          } else {
            this.$message.error(data.msg  || '保存失败')
          }
          loading.close()
        }).catch((err) => {
          this.$message.error('保存失败')
          loading.close()
        })
      },
    }
  }
  </script>
  <style scoped>
  .dialog-footer{
    text-align: right;
  }
  </style>
