<template>
  <section>
    <HaokjBtn></HaokjBtn>
  <el-tabs v-model="activeName" @tab-click="handleClick" class="el-tabs-cls"  type="border-card">
    <el-tab-pane label="进票" name="first">
      <enter-ticket></enter-ticket>
      <!-- todo -->
    </el-tab-pane>
    <el-tab-pane label="销票" name="second">
      <sell-tickets></sell-tickets>
      <!-- todo -->
    </el-tab-pane>
    <!-- <el-tab-pane label="船" name="second"> -->
      <!-- 暂无 赵一菲上传 船舶成本 发票 进项 -->
    <!-- </el-tab-pane> -->
    <el-tab-pane label="oa发票-预付款" name="third">
      <index-list-by-self></index-list-by-self>
      <!-- todo -->
    </el-tab-pane>

  </el-tabs>
  </section>
</template>
<script>
import commodity from './commodity.vue';
import indexListBySelf from './indexListBySelf.vue';
import enterTicket from './enterTicket.vue';
import sellTickets from './sellTickets.vue';
import HaokjBtn from '@/components/AccModel/haokjBtn.vue'
export default{
  name: 'CertificateManagementBill',
  components: { indexListBySelf,commodity,enterTicket,sellTickets,HaokjBtn },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
.el-tabs-cls{

}
</style>
