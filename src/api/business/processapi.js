import request from '@/utils/request'
import {COMPANY_NAMES} from '@/utils/auth'
const BASE_REQUEST = '/api/sysProcess'
const BASE_REQUEST_PAYMENT = '/api/businessPayment'


export function getProcessList() {
  return request({
    url: `${BASE_REQUEST}/getProcessList`,
    method: 'get'
  })
}

export function imprestFundProcessList({pageSize,pageNum,status,fundId}) {
  return request({
    url: `${BASE_REQUEST}/imprestFundProcessList`,
    method: 'get',
    params: {
      pageSize,pageNum,status,fundId
    }
  })
}
export function imprestFundChangeProcessList({pageSize,pageNum,status,fundId}) {
  return request({
    url: `${BASE_REQUEST}/imprestFundChangeProcessList`,
    method: 'get',
    params: {
      pageSize,pageNum,status,fundId
    }
  })
}

export function getMyprocess(query) {
  return request({
    url: `${BASE_REQUEST}/getMyprocess`,
    method: 'get',
    params: {
      name: query.name,
      summary: query.summary,
      paymentSumm: query.paymentSumm,
      accQuery: query.accQuery,
      pageSize: query.pageSize,
      pageNum: query.pageNum,
      startTime: query.startTime,
      endTime: query.endTime,
      ongoing: query.ongoing
    }
  })
}

export function getMyprocessing(query) {
  return request({
    url: `${BASE_REQUEST}/getMyprocessing`,
    method: 'get',
    params: { name: query.name,
      pageSize: query.pageSize,
      pageNum: query.pageNum,
      startTime: query.startTime,
      endTime: query.endTime
    }
  })
}

export function getMyprocessed(query) {
  return request({
    url: `${BASE_REQUEST}/getMyprocessed`,
    method: 'get',
    params: { name: query.name,
      pageSize: query.pageSize,
      pageNum: query.pageNum,
      startTime: query.startTime,
      endTime: query.endTime,
      summary: query.summary,
      status: query.status,
      fukuanName: query.fukuanName
    }
  })
}

export function getMyProcessByCcs(query) {
  return request({
    url: `${BASE_REQUEST}/getMyProcessByCcs`,
    method: 'get',
    params: { name: query.name,
      pageSize: query.pageSize,
      pageNum: query.pageNum,
      startTime: query.startTime,
      endTime: query.endTime,
      summary: query.summary,
      status: query.status,
      fukuanName: query.fukuanName
    }
  })
}

export function managementExpenseDetailSheet(query) {
  return request({
    url: `${BASE_REQUEST_PAYMENT}/managementExpenseDetailSheet`,
    method: 'get',
    params: {
      code: query.code,
      pageSize: query.pageSize,
      pageNo: query.pageNo,
      userName: query.userName,
      startTime: query.startTime,
      endTime: query.endTime,
      companyNames:COMPANY_NAMES,
      region:query.region
    }
  })
}

export function getPaymentProcessListCgPayment(query) {
  return request({
    url: `${BASE_REQUEST_PAYMENT}/getPaymentProcessListCgPayment`,
    method: 'get',
    params: {
      code: query.code,
      pageSize: query.pageSize,
      pageNo: query.pageNo,
      userName: query.userName,
      applyStatus: query.applyStatus,
      startTime: query.startTime,
      endTime: query.endTime,
      companyId: query.companyid,
      money: query.money,
      accid: query.accid
    }
  })
}
export function getPaymentProcessListCg(query) {
  return request({
    url: `${BASE_REQUEST_PAYMENT}/getPaymentProcessListCg`,
    method: 'get',
    params: {
      code: query.code,
      pageSize: query.pageSize,
      pageNo: query.pageNo,
      userName: query.userName,
      applyStatus: query.applyStatus,
      startTime: query.startTime,
      endTime: query.endTime,
      companyId: query.companyid,
      money: query.money,
      accid: query.accid
    }
  })
}

export function getPaymentProcessList(query) {
  return request({
    url: `${BASE_REQUEST_PAYMENT}/getPaymentProcessList`,
    method: 'get',
    params: {
      code: query.code,
      pageSize: query.pageSize,
      pageNo: query.pageNo,
      userName: query.userName,
      applyStatus: query.applyStatus,
      startTime: query.startTime,
      endTime: query.endTime,
      companyId: query.companyid,
      money: query.money,
      accid: query.accid,
      bankAccountName: query.bankAccountName
    }
  })
}

export function taskGetPayment(processId) {
  return request({
    url: `${BASE_REQUEST_PAYMENT}/taskGetPayment`,
    method: 'get',
    params: { processId: processId }
  })
}
export function getprocesspeople(processid) {
  return request({
    url: `${BASE_REQUEST}/getprocesspeople`,
    method: 'get',
    params: { processid: processid }
  })
}

export function getVoucherList(processid,type,single,money) {
  return request({
    url: `${BASE_REQUEST}/getVoucherList`,
    method: 'get',
    params: { id: processid,type,single,money }
  })
}
export function getVoucherListPost(processid,type,single,money,applys) {
  return request({
    url: `${BASE_REQUEST}/getVoucherListPost`,
    method: 'post',
    data: { id: processid,type,single,money,applys }
  })
}
export function getAcctgTransList(processid,type) {
  return request({
    url: `${BASE_REQUEST}/getAcctgTransList`,
    method: 'get',
    params: { id: processid,type }
  })
}

export function getAcctgTransListByCode(processid,code) {
  return request({
    url: `${BASE_REQUEST}/getAcctgTransListByCode`,
    method: 'get',
    params: { id: processid,code }
  })
}
export function saveVoucher(processid, type,month, list,single='',appCode='') {
  return request({
    url: `${BASE_REQUEST}/saveVoucher`,
    method: 'post',
    data: { processId: processid, type,month, certTemplVos:list,single,appCode }
  })
}
export function getIsCodeByPid(processids,type) {
  return request({
    url: `${BASE_REQUEST}/getIsCodeByPid`,
    method: 'get',
    params: { ids: processids,type }
  })
}

export function getIsCodeByPidAndCode(processids,code) {
  return request({
    url: `${BASE_REQUEST}/getIsCodeByPidAndCode`,
    method: 'get',
    params: { ids: processids,code }
  })
}
export function getHaokjSub(appCode) {
  return request({
    url: `${BASE_REQUEST}/getHaokjSub`,
    method: 'get',
    params: {appCode}
  })
}

export function getWageTemplate(list) {
  return request({
    url: `${BASE_REQUEST}/getWageTemplate`,
    method: 'post',
    data: { data: list}
  })
}
export function updateVoucher(json) {
  return request({
    url: `${BASE_REQUEST}/updateVoucher`,
    method: 'post',
    data: json
  })
}

export function getVocherListByCode(processid,code) {
  return request({
    url: `${BASE_REQUEST}/getVocherListByCode`,
    method: 'get',
    params: { id: processid,code }
  })
}


export function getVoucherStatusByReceIds(receiptIds,code='') {
  return request({
    url: `${BASE_REQUEST}/getVoucherStatusByReceIds`,
    method: 'post',
    data: { receiptIds: receiptIds,code }
  })
}


export function getVoucherStatusByPaymentIds(receiptIds,code='') {
  return request({
    url: `${BASE_REQUEST}/getVoucherStatusByPaymentIds`,
    method: 'post',
    data: { receiptIds: receiptIds,code }
  })
}

export function getReceiptVoucher(rwId) {
  return request({
    url: `${BASE_REQUEST}/getReceiptVoucher`,
    method: 'get',
    params: { rwId }
  })
}

export function getPaymentVoucher(pwId) {
  return request({
    url: `${BASE_REQUEST}/getPaymentVoucher`,
    method: 'get',
    params: { pwId }
  })
}


export function getInvoiceVocherListByCode(id,code,dataType='') {
  return request({
    url: `${BASE_REQUEST}/getInvoiceVocherListByCode`,
    method: 'get',
    params: { id: id,code ,dataType}
  })
}

export function getInvoiceVocherListByData(id,code,data) {
  return request({
    url: `${BASE_REQUEST}/getInvoiceVocherListByData`,
    method: 'post',
    data: { id: id,code,data}
  })
}
export function getStowageVocherListByCode(id,code) {
  return request({
    url: `${BASE_REQUEST}/getStowageVocherListByCode`,
    method: 'get',
    params: { id: id,code }
  })
}
export function getUpdateStowageVocherListByCode(id,code) {
  return request({
    url: `${BASE_REQUEST}/getUpdateStowageVocherListByCode`,
    method: 'get',
    params: { id: id,code }
  })
}
